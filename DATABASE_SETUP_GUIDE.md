# 🗄️ Database Setup Guide

## Problem Solved ✅

Your application was configured to use MySQL in production but was actually connecting to a local SQLite database. This has been fixed!

## What Was Changed

1. **Fixed `server/db.ts`** - Now dynamically connects to the correct database based on `DATABASE_URL`
2. **Added PostgreSQL support** - Added `postgres` package for future PostgreSQL support
3. **Created setup scripts** - Automated MySQL database setup and data migration
4. **Added npm scripts** - Easy commands to manage database operations

## 🚀 Quick Setup (Recommended)

### Step 1: Install Dependencies
```bash
npm install
```

### Step 2: Setup MySQL Database and Migrate Data
```bash
npm run db:setup-and-migrate
```

This single command will:
- Create all necessary tables in your MySQL database
- Migrate any existing data from SQLite to MySQL

### Step 3: Restart Your Application
```bash
npm run build:prod
npm start
```

## 📋 Manual Setup (Alternative)

If you prefer to run steps individually:

### 1. Setup MySQL Tables Only
```bash
npm run db:setup-mysql
```

### 2. Migrate Data from SQLite (Optional)
```bash
npm run db:migrate-to-mysql
```

## 🔍 Verification

After setup, your application will:

1. **Connect to MySQL** - You'll see "✅ MySQL database connected successfully" in the logs
2. **Create/Use Tables** - All data will now be stored in your MySQL database
3. **Show Data** - Your admin panel will display all migrated data

## 🗂️ Database Schema

Your MySQL database now contains these tables:
- `users` - Admin authentication
- `products` - Product catalog
- `invoices` - Order/invoice records
- `custom_checkout_pages` - Custom checkout configurations
- `allowed_emails` - Email domain restrictions
- `smtp_providers` - Email service configurations
- `email_templates` - Email template management
- `paypal_buttons` - PayPal button configurations
- `custom_invoices` - Custom invoice management
- `system_messages` - System message templates

## 🔧 Environment Variables

Make sure your `.env.production` file contains:
```env
DATABASE_URL=mysql://user:XwB8rMNKB3OZzBzyYZft@localhost:3306/db
SESSION_SECRET=q0TarlhH8LQM8oks
NODE_ENV=production
SECURE_COOKIES=true
```

## 🐛 Troubleshooting

### Connection Issues
If you see connection errors:
1. Verify your MySQL credentials in `.env.production`
2. Ensure MySQL server is running
3. Check that the database `db` exists
4. Verify user permissions

### Migration Issues
If migration fails:
1. Check if SQLite file exists at `./data.db`
2. Ensure MySQL tables are created first
3. Run setup and migration separately:
   ```bash
   npm run db:setup-mysql
   npm run db:migrate-to-mysql
   ```

### Data Not Showing
If data doesn't appear:
1. Check application logs for database connection messages
2. Verify `NODE_ENV=production` is set
3. Restart the application after database setup

## 📊 Database Support

Your application now supports:
- **SQLite** - For development (`DATABASE_URL=sqlite:./data.db`)
- **MySQL** - For production (`DATABASE_URL=mysql://...`)
- **PostgreSQL** - For enterprise (`DATABASE_URL=postgres://...`)

The database type is automatically detected from the `DATABASE_URL` format.

## 🎉 Success!

Once setup is complete, your application will:
- ✅ Connect to your MySQL database
- ✅ Store all new data in MySQL
- ✅ Display existing data (if migrated)
- ✅ Work exactly as before, but with proper database persistence

Your CloudPanel MySQL database will no longer be empty! 🚀
