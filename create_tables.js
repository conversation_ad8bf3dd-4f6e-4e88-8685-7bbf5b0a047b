import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function createMissingTables() {
  const connection = await mysql.createConnection({
    uri: process.env.DATABASE_URL
  });

  try {
    console.log('🗄️ Creating missing tables...');

    // Create app_config table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS \`app_config\` (
        \`key_name\` varchar(255) NOT NULL,
        \`value\` text NOT NULL,
        \`updated_at\` varchar(255) NOT NULL,
        CONSTRAINT \`app_config_key_name\` PRIMARY KEY(\`key_name\`)
      )
    `);
    console.log('✅ app_config table created');

    // Create telegram_pending_requests table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS \`telegram_pending_requests\` (
        \`id\` varchar(255) NOT NULL,
        \`type\` varchar(50) NOT NULL,
        \`data\` text NOT NULL,
        \`expires_at\` varchar(255) NOT NULL,
        \`created_at\` varchar(255) NOT NULL,
        CONSTRAINT \`telegram_pending_requests_id\` PRIMARY KEY(\`id\`)
      )
    `);
    console.log('✅ telegram_pending_requests table created');

    // Create telegram_processed_updates table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS \`telegram_processed_updates\` (
        \`update_id\` int NOT NULL,
        \`processed_at\` varchar(255) NOT NULL,
        CONSTRAINT \`telegram_processed_updates_update_id\` PRIMARY KEY(\`update_id\`)
      )
    `);
    console.log('✅ telegram_processed_updates table created');

    console.log('🎉 All missing tables created successfully!');
  } catch (error) {
    console.error('❌ Error creating tables:', error);
  } finally {
    await connection.end();
  }
}

createMissingTables();
