import React from "react";
import { createRoot } from "react-dom/client";
import "./index.css";

console.log("🔍 main.tsx executing...");

// Minimal test App to isolate the issue
function MinimalApp() {
  console.log("🔍 MinimalApp rendering...");

  return (
    <div style={{
      padding: "40px",
      backgroundColor: "#2196F3",
      color: "white",
      textAlign: "center",
      fontFamily: "Arial, sans-serif",
      minHeight: "100vh"
    }}>
      <h1 style={{ fontSize: "48px", margin: "20px 0" }}>🔧 DEBUGGING APP</h1>
      <p style={{ fontSize: "24px", margin: "10px 0" }}>React is working! Now testing components...</p>

      <div style={{
        backgroundColor: "rgba(255,255,255,0.1)",
        padding: "20px",
        margin: "20px 0",
        borderRadius: "10px"
      }}>
        <h2>Component Tests:</h2>
        <TestRouter />
        <TestActualApp />
      </div>
    </div>
  );
}

// Test the router separately
function TestRouter() {
  console.log("🔍 TestRouter rendering...");

  return (
    <div style={{ margin: "10px 0" }}>
      <p>✅ Router test (fixed)</p>
      <TestRouterComponent />
    </div>
  );
}

// Separate component to test router
function TestRouterComponent() {
  // For now, let's just test if we can render without the router
  return (
    <div style={{ backgroundColor: "rgba(0,255,0,0.2)", padding: "10px", margin: "5px" }}>
      ✅ Component rendering works! Router test skipped for now.
      <br />
      <small>Next: Test the actual App component</small>
    </div>
  );
}

// Test the actual App component
function TestActualApp() {
  console.log("🔍 TestActualApp rendering...");

  // Try to import App component directly to see the exact error
  try {
    // First, let's try to import App synchronously to catch the error
    console.log("🔍 Attempting to import App component...");

    return (
      <div style={{ margin: "10px 0" }}>
        <p>🔍 Testing actual App component...</p>
        <div style={{ backgroundColor: "rgba(255,255,0,0.2)", padding: "10px", margin: "5px" }}>
          <p>⚠️ App component test - Loading...</p>
          <ActualAppTest />
        </div>
      </div>
    );
  } catch (error) {
    console.error("❌ App component error:", error);
    return (
      <div style={{ backgroundColor: "rgba(255,0,0,0.2)", padding: "10px", margin: "5px" }}>
        ❌ App Component Error: {error.message}
        <br />
        <small>Stack: {error.stack}</small>
      </div>
    );
  }
}

// Import and test the actual App
function ActualAppTest() {
  const [importResult, setImportResult] = React.useState("Not tested yet");
  const [importError, setImportError] = React.useState(null);

  const testAppImport = async () => {
    try {
      console.log("🔍 Testing App component import...");
      setImportResult("⏳ Importing...");
      setImportError(null);

      const AppModule = await import('./App');
      console.log("✅ App module imported successfully:", AppModule);
      setImportResult("✅ App imported successfully!");

      // Try to render the App component
      const App = AppModule.default;
      console.log("🔍 App component:", App);

    } catch (error) {
      console.error("❌ App import failed:", error);
      setImportResult("❌ App import failed");
      setImportError(error);
    }
  };

  try {
    return (
      <div style={{ backgroundColor: "rgba(0,0,255,0.2)", padding: "10px", margin: "5px" }}>
        <p>🔍 App component import test</p>
        <p><small>Status: {importResult}</small></p>
        {importError && (
          <div style={{ backgroundColor: "rgba(255,0,0,0.3)", padding: "5px", margin: "5px 0", fontSize: "12px" }}>
            <strong>Error:</strong> {importError.message}
            <br />
            <strong>Stack:</strong> {importError.stack?.substring(0, 200)}...
          </div>
        )}
        <button
          style={{
            padding: "10px 20px",
            backgroundColor: "#4CAF50",
            color: "white",
            border: "none",
            borderRadius: "5px",
            cursor: "pointer",
            margin: "5px 0"
          }}
          onClick={testAppImport}
        >
          Test App Import
        </button>
      </div>
    );
  } catch (error) {
    console.error("❌ ActualAppTest error:", error);
    return (
      <div style={{ backgroundColor: "rgba(255,0,0,0.3)", padding: "5px" }}>
        ❌ ActualAppTest Error: {error.message}
      </div>
    );
  }
}

const rootElement = document.getElementById("root");
console.log("🔍 Root element found:", !!rootElement);

if (rootElement) {
  console.log("✅ Creating React root...");
  const root = createRoot(rootElement);

  console.log("🚀 Rendering MinimalApp...");

  try {
    root.render(<MinimalApp />);
    console.log("✅ MinimalApp rendered successfully");
  } catch (error) {
    console.error("❌ Error rendering MinimalApp:", error);
    // Fallback to simple content if MinimalApp fails
    root.render(
      <div style={{
        padding: "40px",
        backgroundColor: "#f44336",
        color: "white",
        textAlign: "center",
        fontFamily: "Arial, sans-serif"
      }}>
        <h1>❌ MinimalApp Error</h1>
        <p>Error: {error.message}</p>
        <pre style={{ backgroundColor: "rgba(0,0,0,0.3)", padding: "20px", textAlign: "left" }}>
          {error.stack}
        </pre>
      </div>
    );
  }
} else {
  console.error("❌ Root element not found!");
}
