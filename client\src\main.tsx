import React from "react";
import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";

console.log("🔍 main.tsx executing...");

const rootElement = document.getElementById("root");
console.log("🔍 Root element found:", !!rootElement);

if (rootElement) {
  console.log("✅ Creating React root...");
  const root = createRoot(rootElement);

  console.log("🚀 Rendering App...");

  // Wrap App in error boundary to catch any errors
  try {
    root.render(<App />);
    console.log("✅ App rendered successfully");
  } catch (error) {
    console.error("❌ Error rendering App:", error);
    // Fallback to simple content if App fails
    root.render(
      <div style={{
        padding: "40px",
        backgroundColor: "#f44336",
        color: "white",
        textAlign: "center",
        fontFamily: "Arial, sans-serif"
      }}>
        <h1>❌ App Component Error</h1>
        <p>Error: {error.message}</p>
        <pre style={{ backgroundColor: "rgba(0,0,0,0.3)", padding: "20px", textAlign: "left" }}>
          {error.stack}
        </pre>
      </div>
    );
  }
} else {
  console.error("❌ Root element not found!");
}
