import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";

console.log("🔍 main.tsx is executing!");
console.log("🔍 Document ready state:", document.readyState);
console.log("🔍 Document URL:", document.URL);

// Detailed DOM inspection
console.log("🔍 Document body:", document.body);
console.log("🔍 Document body innerHTML length:", document.body?.innerHTML?.length);
console.log("🔍 Document body children:", document.body?.children);

// Check if root element exists
const rootElement = document.getElementById("root");
console.log("🔍 Root element:", rootElement);

// Check all elements with any ID
const allElementsWithId = document.querySelectorAll("[id]");
console.log("🔍 All elements with ID:", Array.from(allElementsWithId).map(el => ({ id: el.id, tagName: el.tagName })));

// Check body content
console.log("🔍 Body HTML:", document.body.innerHTML);

if (!rootElement) {
  console.error("❌ Root element not found!");

  // Try to find any element that might be the root
  const possibleRoots = document.querySelectorAll("div");
  console.log("🔍 All div elements:", Array.from(possibleRoots).map(div => ({
    id: div.id,
    className: div.className,
    innerHTML: div.innerHTML.substring(0, 100)
  })));

  // Create a new root element
  console.log("🔧 Creating new root element...");
  const newRoot = document.createElement("div");
  newRoot.id = "root";
  document.body.appendChild(newRoot);

  newRoot.innerHTML = `
    <div style="
      padding: 40px;
      background: orange;
      color: white;
      text-align: center;
      font-family: Arial, sans-serif;
    ">
      <h1>⚠️ ROOT ELEMENT WAS MISSING!</h1>
      <p>The #root element was missing, but I created a new one.</p>
      <p>This suggests something is removing the root element from the DOM.</p>
      <div style="background: rgba(0,0,0,0.3); padding: 20px; margin: 20px; text-align: left;">
        <h3>Debug Info:</h3>
        <p><strong>Document URL:</strong> ${document.URL}</p>
        <p><strong>Body children count:</strong> ${document.body.children.length}</p>
        <p><strong>All IDs found:</strong> ${Array.from(allElementsWithId).map(el => el.id).join(", ")}</p>
      </div>
    </div>
  `;
} else {
  console.log("✅ Root element found, creating React root...");

  try {
    const root = createRoot(rootElement);
    console.log("✅ React root created successfully");

    console.log("🚀 Rendering App component...");
    root.render(<App />);
    console.log("✅ App component rendered successfully");

  } catch (error) {
    console.error("❌ Error creating React root or rendering App:", error);
    rootElement.innerHTML = `
      <div style="
        padding: 40px;
        background: red;
        color: white;
        text-align: center;
        font-family: Arial, sans-serif;
      ">
        <h1>❌ REACT ERROR!</h1>
        <p>Error: ${error.message}</p>
        <pre style="background: rgba(0,0,0,0.3); padding: 20px; margin: 20px; text-align: left;">
          ${error.stack}
        </pre>
      </div>
    `;
  }
}
