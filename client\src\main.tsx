import React from "react";
import { createRoot } from "react-dom/client";
import "./index.css";

console.log("🔍 main.tsx executing...");

// Minimal test App to isolate the issue
function MinimalApp() {
  console.log("🔍 MinimalApp rendering...");

  return (
    <div style={{
      padding: "40px",
      backgroundColor: "#2196F3",
      color: "white",
      textAlign: "center",
      fontFamily: "Arial, sans-serif",
      minHeight: "100vh"
    }}>
      <h1 style={{ fontSize: "48px", margin: "20px 0" }}>🔧 DEBUGGING APP</h1>
      <p style={{ fontSize: "24px", margin: "10px 0" }}>React is working! Now testing components...</p>

      <div style={{
        backgroundColor: "rgba(255,255,255,0.1)",
        padding: "20px",
        margin: "20px 0",
        borderRadius: "10px"
      }}>
        <h2>Component Tests:</h2>
        <TestRouter />
      </div>
    </div>
  );
}

// Test the router separately
function TestRouter() {
  console.log("🔍 TestRouter rendering...");

  try {
    // Import router dynamically to catch any router errors
    const { Router, Route } = require("wouter");

    return (
      <div style={{ margin: "10px 0" }}>
        <p>✅ Router imported successfully</p>
        <Router>
          <Route path="/">
            <div style={{ backgroundColor: "rgba(0,255,0,0.2)", padding: "10px", margin: "5px" }}>
              ✅ Router is working - Home route matched!
            </div>
          </Route>
          <Route>
            <div style={{ backgroundColor: "rgba(255,255,0,0.2)", padding: "10px", margin: "5px" }}>
              ⚠️ Fallback route
            </div>
          </Route>
        </Router>
      </div>
    );
  } catch (error) {
    console.error("❌ Router error:", error);
    return (
      <div style={{ backgroundColor: "rgba(255,0,0,0.2)", padding: "10px", margin: "5px" }}>
        ❌ Router Error: {error.message}
      </div>
    );
  }
}

const rootElement = document.getElementById("root");
console.log("🔍 Root element found:", !!rootElement);

if (rootElement) {
  console.log("✅ Creating React root...");
  const root = createRoot(rootElement);

  console.log("🚀 Rendering MinimalApp...");

  try {
    root.render(<MinimalApp />);
    console.log("✅ MinimalApp rendered successfully");
  } catch (error) {
    console.error("❌ Error rendering MinimalApp:", error);
    // Fallback to simple content if MinimalApp fails
    root.render(
      <div style={{
        padding: "40px",
        backgroundColor: "#f44336",
        color: "white",
        textAlign: "center",
        fontFamily: "Arial, sans-serif"
      }}>
        <h1>❌ MinimalApp Error</h1>
        <p>Error: {error.message}</p>
        <pre style={{ backgroundColor: "rgba(0,0,0,0.3)", padding: "20px", textAlign: "left" }}>
          {error.stack}
        </pre>
      </div>
    );
  }
} else {
  console.error("❌ Root element not found!");
}
