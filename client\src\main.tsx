import React from "react";
import { createRoot } from "react-dom/client";
import "./index.css";

console.log("🔍 main.tsx executing...");

// Simple test component to see if React works at all
function SimpleTest() {
  return (
    <div style={{
      position: "fixed",
      top: "0",
      left: "0",
      width: "100vw",
      height: "100vh",
      padding: "40px",
      backgroundColor: "#4CAF50",
      color: "white",
      textAlign: "center",
      fontFamily: "Arial, sans-serif",
      fontSize: "24px",
      zIndex: "9999",
      display: "flex",
      flexDirection: "column",
      justifyContent: "center",
      alignItems: "center"
    }}>
      <h1 style={{ fontSize: "48px", margin: "20px 0" }}>🎉 REACT IS WORKING!</h1>
      <p style={{ fontSize: "24px", margin: "10px 0" }}>If you can see this, <PERSON>act is rendering successfully!</p>
      <p style={{ fontSize: "20px", margin: "10px 0" }}>Time: {new Date().toLocaleTimeString()}</p>
      <p style={{ fontSize: "16px", margin: "10px 0", backgroundColor: "rgba(0,0,0,0.3)", padding: "10px", borderRadius: "5px" }}>
        This green screen means React is working! The white page issue is solved!
      </p>
    </div>
  );
}

const rootElement = document.getElementById("root");
console.log("🔍 Root element found:", !!rootElement);

if (rootElement) {
  console.log("✅ Creating React root...");
  const root = createRoot(rootElement);

  console.log("🚀 Rendering SimpleTest...");
  root.render(<SimpleTest />);
  console.log("✅ SimpleTest rendered successfully");

  // Add a simple test to see if anything is in the DOM after a short delay
  setTimeout(() => {
    console.log("🔍 DOM content after 1 second:", document.body.innerHTML.length > 1000 ? "Content found" : "Still empty");
    console.log("🔍 Root element children:", rootElement.children.length);
    console.log("🔍 Root element HTML:", rootElement.innerHTML.substring(0, 200));
  }, 1000);
} else {
  console.error("❌ Root element not found!");
}
