import React from "react";
import { createRoot } from "react-dom/client";
import "./index.css";

console.log("🔍 main.tsx executing...");

// Minimal test App to isolate the issue
function MinimalApp() {
  console.log("🔍 MinimalApp rendering...");

  return (
    <div style={{
      padding: "40px",
      backgroundColor: "#2196F3",
      color: "white",
      textAlign: "center",
      fontFamily: "Arial, sans-serif",
      minHeight: "100vh"
    }}>
      <h1 style={{ fontSize: "48px", margin: "20px 0" }}>🔧 DEBUGGING APP</h1>
      <p style={{ fontSize: "24px", margin: "10px 0" }}>React is working! Now testing components...</p>

      <div style={{
        backgroundColor: "rgba(255,255,255,0.1)",
        padding: "20px",
        margin: "20px 0",
        borderRadius: "10px"
      }}>
        <h2>Component Tests:</h2>
        <TestRouter />
        <TestActualApp />
      </div>
    </div>
  );
}

// Test the router separately
function TestRouter() {
  console.log("🔍 TestRouter rendering...");

  try {
    // Import router using dynamic import
    const React = window.React || require('react');

    // Test if we can access wouter from window or import it
    return (
      <div style={{ margin: "10px 0" }}>
        <p>✅ Testing router import...</p>
        <TestRouterComponent />
      </div>
    );
  } catch (error) {
    console.error("❌ Router error:", error);
    return (
      <div style={{ backgroundColor: "rgba(255,0,0,0.2)", padding: "10px", margin: "5px" }}>
        ❌ Router Error: {error.message}
      </div>
    );
  }
}

// Separate component to test router
function TestRouterComponent() {
  // For now, let's just test if we can render without the router
  return (
    <div style={{ backgroundColor: "rgba(0,255,0,0.2)", padding: "10px", margin: "5px" }}>
      ✅ Component rendering works! Router test skipped for now.
      <br />
      <small>Next: Test the actual App component</small>
    </div>
  );
}

// Test the actual App component
function TestActualApp() {
  console.log("🔍 TestActualApp rendering...");

  try {
    // Import the actual App component dynamically
    const React = window.React;

    return (
      <div style={{ margin: "10px 0" }}>
        <p>🔍 Testing actual App component...</p>
        <div style={{ backgroundColor: "rgba(255,255,0,0.2)", padding: "10px", margin: "5px" }}>
          <p>⚠️ App component test - Loading...</p>
          <ActualAppTest />
        </div>
      </div>
    );
  } catch (error) {
    console.error("❌ App component error:", error);
    return (
      <div style={{ backgroundColor: "rgba(255,0,0,0.2)", padding: "10px", margin: "5px" }}>
        ❌ App Component Error: {error.message}
      </div>
    );
  }
}

// Import and test the actual App
function ActualAppTest() {
  try {
    // This will help us see if the App component itself is the issue
    return (
      <div style={{ backgroundColor: "rgba(0,0,255,0.2)", padding: "10px", margin: "5px" }}>
        <p>🔍 App component would render here...</p>
        <p><small>If you see this, the App import is working</small></p>
        <button
          style={{
            padding: "10px 20px",
            backgroundColor: "#4CAF50",
            color: "white",
            border: "none",
            borderRadius: "5px",
            cursor: "pointer"
          }}
          onClick={() => {
            console.log("🔍 Testing App component import...");
            // We'll test importing the actual App component
            import('./App').then(AppModule => {
              console.log("✅ App module imported successfully:", AppModule);
            }).catch(error => {
              console.error("❌ App import failed:", error);
            });
          }}
        >
          Test App Import
        </button>
      </div>
    );
  } catch (error) {
    console.error("❌ ActualAppTest error:", error);
    return (
      <div style={{ backgroundColor: "rgba(255,0,0,0.3)", padding: "5px" }}>
        ❌ ActualAppTest Error: {error.message}
      </div>
    );
  }
}

const rootElement = document.getElementById("root");
console.log("🔍 Root element found:", !!rootElement);

if (rootElement) {
  console.log("✅ Creating React root...");
  const root = createRoot(rootElement);

  console.log("🚀 Rendering MinimalApp...");

  try {
    root.render(<MinimalApp />);
    console.log("✅ MinimalApp rendered successfully");
  } catch (error) {
    console.error("❌ Error rendering MinimalApp:", error);
    // Fallback to simple content if MinimalApp fails
    root.render(
      <div style={{
        padding: "40px",
        backgroundColor: "#f44336",
        color: "white",
        textAlign: "center",
        fontFamily: "Arial, sans-serif"
      }}>
        <h1>❌ MinimalApp Error</h1>
        <p>Error: {error.message}</p>
        <pre style={{ backgroundColor: "rgba(0,0,0,0.3)", padding: "20px", textAlign: "left" }}>
          {error.stack}
        </pre>
      </div>
    );
  }
} else {
  console.error("❌ Root element not found!");
}
