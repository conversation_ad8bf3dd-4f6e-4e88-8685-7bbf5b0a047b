import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";

console.log("🔍 main.tsx is executing!");

// Check if root element exists
const rootElement = document.getElementById("root");
console.log("🔍 Root element:", rootElement);

if (!rootElement) {
  console.error("❌ Root element not found!");
  document.body.innerHTML = `
    <div style="
      padding: 40px;
      background: red;
      color: white;
      text-align: center;
      font-family: Arial, sans-serif;
    ">
      <h1>❌ ROOT ELEMENT NOT FOUND!</h1>
      <p>The #root element is missing from the HTML</p>
    </div>
  `;
} else {
  console.log("✅ Root element found, creating React root...");

  try {
    const root = createRoot(rootElement);
    console.log("✅ React root created successfully");

    console.log("🚀 Rendering App component...");
    root.render(<App />);
    console.log("✅ App component rendered successfully");

  } catch (error) {
    console.error("❌ Error creating React root or rendering App:", error);
    rootElement.innerHTML = `
      <div style="
        padding: 40px;
        background: red;
        color: white;
        text-align: center;
        font-family: Arial, sans-serif;
      ">
        <h1>❌ REACT ERROR!</h1>
        <p>Error: ${error.message}</p>
        <pre style="background: rgba(0,0,0,0.3); padding: 20px; margin: 20px; text-align: left;">
          ${error.stack}
        </pre>
      </div>
    `;
  }
}
