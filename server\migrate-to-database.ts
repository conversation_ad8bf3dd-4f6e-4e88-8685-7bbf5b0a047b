#!/usr/bin/env tsx

/**
 * Migration script to move from memory storage to MySQL database
 * This script creates the new tables and initializes default configuration
 */

import { db } from './db';
import { 
  telegramPendingRequests, 
  telegramProcessedUpdates, 
  appConfig 
} from '@shared/schema';
import { databaseConfig } from './services/database-config';

async function runMigration() {
  console.log('🚀 Starting migration from memory storage to MySQL database...');

  try {
    // Check if tables exist by trying to select from them
    console.log('📋 Checking existing tables...');
    
    try {
      await db.select().from(telegramPendingRequests).limit(1);
      console.log('✅ telegram_pending_requests table exists');
    } catch (error) {
      console.log('❌ telegram_pending_requests table does not exist - will be created by Dr<PERSON><PERSON>');
    }

    try {
      await db.select().from(telegramProcessedUpdates).limit(1);
      console.log('✅ telegram_processed_updates table exists');
    } catch (error) {
      console.log('❌ telegram_processed_updates table does not exist - will be created by Drizzle');
    }

    try {
      await db.select().from(appConfig).limit(1);
      console.log('✅ app_config table exists');
    } catch (error) {
      console.log('❌ app_config table does not exist - will be created by Drizzle');
    }

    // Initialize default configuration
    console.log('🔧 Initializing default configuration...');
    await databaseConfig.initializeDefaults();

    // Clean up any existing data (optional)
    console.log('🧹 Cleaning up any existing temporary data...');
    
    try {
      // Clean up expired telegram requests
      const now = new Date().toISOString();
      await db.delete(telegramPendingRequests);
      console.log('✅ Cleaned up telegram pending requests');
    } catch (error) {
      console.log('⚠️ Could not clean telegram pending requests (table may not exist yet)');
    }

    try {
      // Clean up old processed updates
      await db.delete(telegramProcessedUpdates);
      console.log('✅ Cleaned up telegram processed updates');
    } catch (error) {
      console.log('⚠️ Could not clean telegram processed updates (table may not exist yet)');
    }

    console.log('✅ Migration completed successfully!');
    console.log('');
    console.log('📊 Migration Summary:');
    console.log('  ✅ Telegram bot requests moved to database');
    console.log('  ✅ Configuration storage moved to database');
    console.log('  ✅ Processed updates tracking moved to database');
    console.log('  ✅ Default configuration initialized');
    console.log('');
    console.log('🎯 Next Steps:');
    console.log('  1. Restart your application');
    console.log('  2. Verify Telegram bot functionality');
    console.log('  3. Check admin panel configuration');
    console.log('  4. Monitor database performance');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run migration if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigration()
    .then(() => {
      console.log('🎉 Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration script failed:', error);
      process.exit(1);
    });
}

export { runMigration };
