import {
  users,
  products,
  invoices,
  customCheckoutPages,
  allowedEmails,
  emailTemplates,
  paypalButtons,
  customInvoices,
  systemMessages,
  type User,
  type InsertUser,
  type Product,
  type InsertProduct,
  type Invoice,
  type InsertInvoice,
  type CustomCheckoutPage,
  type InsertCustomCheckoutPage,
  type AllowedEmail,
  type InsertAllowedEmail,
  type EmailTemplate,
  type InsertEmailTemplate,
  type PaypalButton,
  type InsertPaypalButton,
  type CustomInvoice,
  type InsertCustomInvoice,
  smtpProviders,
  type SmtpProvider,
  type InsertSmtpProvider
} from "@shared/schema";
import { EmbedCode } from '@shared/embed-codes';
import { createHash, randomBytes } from 'crypto';
import { db } from './db';
import { eq, sql, and } from 'drizzle-orm';

// Device interface
interface Device {
  id: string;
  name: string;
  ip: string;
  userAgent: string;
  lastLogin: string;
  createdAt: string;
}

// Recovery code interface
interface RecoveryCode {
  code: string;
  used: boolean;
}

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  verifyUserCredentials(username: string, password: string): Promise<boolean>;
  verifyUserCredentialsWithUser(user: User, password: string): Promise<boolean>;
  saveResetToken(userId: number, token: string, expiry: Date): Promise<void>;
  validateResetToken(token: string): Promise<boolean>;
  getUserByResetToken(token: string): Promise<User | undefined>;
  updateUserPassword(userId: number, password: string): Promise<void>;
  clearResetToken(userId: number): Promise<void>;
  updateUsername(userId: number, username: string): Promise<void>;
  updateAutoLoginSettings(userId: number, rememberMe: boolean): Promise<void>;
  enableTwoFactor(userId: number, secret: string): Promise<void>;
  disableTwoFactor(userId: number): Promise<void>;
  verifyTwoFactorToken(userId: number, token: string): Promise<boolean>;

  // Recovery code methods
  generateRecoveryCodes(userId: number): Promise<string[]>;
  verifyRecoveryCode(userId: number, code: string): Promise<boolean>;

  // Device tracking methods
  addDevice(userId: number, deviceInfo: Omit<Device, 'id' | 'createdAt' | 'lastLogin'>): Promise<Device>;
  getDevices(userId: number): Promise<Device[]>;
  updateDeviceLastLogin(userId: number, deviceId: string): Promise<void>;
  removeDevice(userId: number, deviceId: string): Promise<boolean>;

  // Product methods
  getProducts(): Promise<Product[]>;
  getProduct(id: number): Promise<Product | undefined>;
  createProduct(product: InsertProduct): Promise<Product>;
  deleteProduct(id: number): Promise<boolean>;
  bulkDeleteProducts(ids: number[]): Promise<{ success: number; failed: number }>;

  // Invoice methods
  createInvoice(invoice: InsertInvoice): Promise<Invoice>;
  getInvoice(id: number): Promise<Invoice | undefined>;
  getInvoices(): Promise<Invoice[]>;
  updateInvoice(id: number, update: Partial<InsertInvoice>): Promise<Invoice | undefined>;
  deleteInvoice(id: number): Promise<boolean>;
  bulkDeleteInvoices(ids: number[]): Promise<{ success: number; failed: number }>;

  // Configuration methods
  getGeneralSettings(): Promise<any>;
  getEmailConfig(): Promise<any>;
  getPaymentConfig(): Promise<any>;

  // Custom Checkout Page methods
  createCustomCheckoutPage(page: InsertCustomCheckoutPage): Promise<CustomCheckoutPage>;
  getCustomCheckoutPage(id: number): Promise<CustomCheckoutPage | undefined>;
  getCustomCheckoutPageBySlug(slug: string): Promise<CustomCheckoutPage | undefined>;
  getCustomCheckoutPages(): Promise<CustomCheckoutPage[]>;
  updateCustomCheckoutPage(id: number, update: Partial<InsertCustomCheckoutPage>): Promise<CustomCheckoutPage | undefined>;
  incrementCustomCheckoutPageViews(id: number): Promise<void>;
  incrementCustomCheckoutPageConversions(id: number): Promise<void>;
  deleteCustomCheckoutPage(id: number): Promise<boolean>;

  // Allowed Email methods
  getAllowedEmails(): Promise<AllowedEmail[]>;
  getAllowedEmail(id: number): Promise<AllowedEmail | undefined>;
  getEmailByAddress(email: string): Promise<AllowedEmail | undefined>;
  isEmailAllowed(email: string): Promise<boolean>;
  createAllowedEmail(email: InsertAllowedEmail): Promise<AllowedEmail>;
  updateAllowedEmail(id: number, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail | undefined>;
  updateOrCreateAllowedEmail(emailAddress: string, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail>;
  deleteAllowedEmail(id: number): Promise<boolean>;
  bulkCreateAllowedEmails(emails: string[]): Promise<{ success: number; failed: number }>;
  bulkDeleteAllowedEmails(ids: number[]): Promise<{ success: number; failed: number }>;

  // Email Template methods
  getEmailTemplates(): Promise<EmailTemplate[]>;
  getEmailTemplate(id: number): Promise<EmailTemplate | undefined>;
  createEmailTemplate(template: InsertEmailTemplate): Promise<EmailTemplate>;
  updateEmailTemplate(id: number, update: Partial<InsertEmailTemplate>): Promise<EmailTemplate | undefined>;
  deleteEmailTemplate(id: number): Promise<boolean>;

  // PayPal Button methods
  getPaypalButtons(): Promise<PaypalButton[]>;
  getPaypalButton(id: number): Promise<PaypalButton | undefined>;
  createPaypalButton(button: InsertPaypalButton): Promise<PaypalButton>;
  updatePaypalButton(id: number, update: Partial<InsertPaypalButton>): Promise<PaypalButton | undefined>;
  deletePaypalButton(id: number): Promise<boolean>;

  // Custom Invoice methods
  getCustomInvoices(): Promise<CustomInvoice[]>;
  getCustomInvoice(id: number): Promise<CustomInvoice | undefined>;
  getCustomInvoiceByNumber(invoiceNumber: string): Promise<CustomInvoice | undefined>;
  createCustomInvoice(invoice: InsertCustomInvoice): Promise<CustomInvoice>;
  updateCustomInvoice(id: number, update: Partial<InsertCustomInvoice>): Promise<CustomInvoice | undefined>;
  incrementCustomInvoiceViewCount(id: number): Promise<void>;
  markCustomInvoiceAsPaid(id: number): Promise<CustomInvoice | undefined>;
  deleteCustomInvoice(id: number): Promise<boolean>;

  // Contact Inquiry methods
  createContactInquiry?(inquiry: any): Promise<any>;
  getContactInquiries?(): Promise<any[]>;
  updateContactInquiry?(id: number, update: any): Promise<any>;

  // Embed Code methods
  getEmbedCodes(): Promise<EmbedCode[]>;
  getEmbedCode(id: string): Promise<EmbedCode | undefined>;
  createEmbedCode(embedCode: EmbedCode): Promise<EmbedCode>;
  updateEmbedCode(id: string, update: Partial<EmbedCode>): Promise<EmbedCode | undefined>;
  deleteEmbedCode(id: string): Promise<boolean>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private products: Map<number, Product>;
  private invoices: Map<number, Invoice>;
  private customCheckoutPages: Map<number, CustomCheckoutPage>;
  private allowedEmails: Map<number, AllowedEmail>;
  private emailTemplates: Map<number, EmailTemplate>;
  private paypalButtons: Map<number, PaypalButton>;
  private customInvoices: Map<number, CustomInvoice>;
  private contactInquiries: Map<number, any>;
  private embedCodes: Map<string, EmbedCode>;
  private db: typeof db;

  userCurrentId: number;
  productCurrentId: number;
  invoiceCurrentId: number;
  customCheckoutPageCurrentId: number;
  allowedEmailCurrentId: number;
  emailTemplateCurrentId: number;
  paypalButtonCurrentId: number;
  customInvoiceCurrentId: number;
  contactInquiryCurrentId: number;


  constructor() {
    this.users = new Map();
    this.products = new Map();
    this.invoices = new Map();
    this.customCheckoutPages = new Map();
    this.allowedEmails = new Map();
    this.emailTemplates = new Map();
    this.paypalButtons = new Map();
    this.customInvoices = new Map();
    this.contactInquiries = new Map();
    this.embedCodes = new Map();
    this.db = db;

    this.userCurrentId = 1;
    this.productCurrentId = 1;
    this.invoiceCurrentId = 1;
    this.customCheckoutPageCurrentId = 1;
    this.allowedEmailCurrentId = 1;
    this.emailTemplateCurrentId = 1;
    this.paypalButtonCurrentId = 1;
    this.customInvoiceCurrentId = 1;
    this.contactInquiryCurrentId = 1;


    // Initialize with some products
    this.initializeProducts();

    // Initialize with some PayPal buttons
    this.initializePaypalButtons();




  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.email === email,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userCurrentId++;

    // Hash the password if it's not already hashed
    let password = insertUser.password;
    if (!password.match(/^[0-9a-f]{64}$/i)) {
      password = createHash('sha256').update(password).digest('hex');
    }

    const user: User = {
      ...insertUser,
      id,
      password,
      rememberMe: insertUser.rememberMe || false,
      resetToken: undefined,
      resetTokenExpiry: undefined,
      twoFactorSecret: undefined,
      twoFactorEnabled: false,
      recoveryCodes: [],
      devices: []
    };

    this.users.set(id, user);
    return user;
  }

  async verifyUserCredentials(username: string, password: string): Promise<boolean> {
    const user = await this.getUserByUsername(username);
    if (!user) return false;

    // Hash the password for comparison
    const hashedPassword = createHash('sha256').update(password).digest('hex');
    return user.password === hashedPassword;
  }

  // Optimized version that takes a user object to avoid double lookup
  async verifyUserCredentialsWithUser(user: User, password: string): Promise<boolean> {
    if (!user) return false;

    // Hash the password for comparison
    const hashedPassword = createHash('sha256').update(password).digest('hex');
    return user.password === hashedPassword;
  }

  async saveResetToken(userId: number, token: string, expiry: Date): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    user.resetToken = token;
    user.resetTokenExpiry = expiry.toISOString();
    this.users.set(userId, user);
  }

  async validateResetToken(token: string): Promise<boolean> {
    const user = await this.getUserByResetToken(token);
    if (!user) return false;

    // Check if token is expired
    if (new Date() > new Date(user.resetTokenExpiry!)) {
      return false;
    }

    return true;
  }

  async getUserByResetToken(token: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.resetToken === token,
    );
  }

  async updateUserPassword(userId: number, password: string): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    user.password = password;
    this.users.set(userId, user);
  }

  async clearResetToken(userId: number): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    user.resetToken = undefined;
    user.resetTokenExpiry = undefined;
    this.users.set(userId, user);
  }

  async updateUsername(userId: number, username: string): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    user.username = username;
    this.users.set(userId, user);
  }

  async updateAutoLoginSettings(userId: number, rememberMe: boolean): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    user.rememberMe = rememberMe;
    this.users.set(userId, user);
  }

  async updateUser(userId: number, updates: Partial<User>): Promise<User | null> {
    const user = await this.getUser(userId);
    if (!user) return null;

    const updatedUser = { ...user, ...updates };
    this.users.set(userId, updatedUser);
    return updatedUser;
  }

  async enableTwoFactor(userId: number, secret: string): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    user.twoFactorSecret = secret;
    user.twoFactorEnabled = true;
    this.users.set(userId, user);
  }

  async disableTwoFactor(userId: number): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    user.twoFactorSecret = undefined;
    user.twoFactorEnabled = false;
    this.users.set(userId, user);
  }

  async verifyTwoFactorToken(userId: number, token: string): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user || !user.twoFactorEnabled || !user.twoFactorSecret) return false;

    // The actual verification is done in the auth routes using otplib
    // This method is just a placeholder for the interface
    return true;
  }

  // Recovery code methods
  async generateRecoveryCodes(userId: number): Promise<string[]> {
    const user = await this.getUser(userId);
    if (!user) return [];

    // Generate 10 recovery codes
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      // Generate a random 8-character code
      const code = randomBytes(4).toString('hex').toUpperCase();
      codes.push(code);

      // Add to user's recovery codes
      user.recoveryCodes.push({
        code,
        used: false
      });
    }

    this.users.set(userId, user);
    return codes;
  }

  async verifyRecoveryCode(userId: number, code: string): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user) return false;

    // Find the recovery code
    const recoveryCodeIndex = user.recoveryCodes.findIndex(rc => rc.code === code && !rc.used);
    if (recoveryCodeIndex === -1) return false;

    // Mark the code as used
    user.recoveryCodes[recoveryCodeIndex].used = true;
    this.users.set(userId, user);

    return true;
  }

  // Device tracking methods
  async addDevice(userId: number, deviceInfo: Omit<Device, 'id' | 'createdAt' | 'lastLogin'>): Promise<Device> {
    const user = await this.getUser(userId);
    if (!user) throw new Error('User not found');

    const now = new Date().toISOString();
    const device: Device = {
      ...deviceInfo,
      id: randomBytes(16).toString('hex'),
      createdAt: now,
      lastLogin: now
    };

    user.devices.push(device);
    this.users.set(userId, user);

    return device;
  }

  async getDevices(userId: number): Promise<Device[]> {
    const user = await this.getUser(userId);
    if (!user) return [];

    return user.devices;
  }

  async updateDeviceLastLogin(userId: number, deviceId: string): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    const deviceIndex = user.devices.findIndex(d => d.id === deviceId);
    if (deviceIndex === -1) return;

    user.devices[deviceIndex].lastLogin = new Date().toISOString();
    this.users.set(userId, user);
  }

  async removeDevice(userId: number, deviceId: string): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user) return false;

    const initialLength = user.devices.length;
    user.devices = user.devices.filter(d => d.id !== deviceId);

    if (user.devices.length === initialLength) {
      return false; // No device was removed
    }

    this.users.set(userId, user);
    return true;
  }

  async getProducts(): Promise<Product[]> {
    return Array.from(this.products.values());
  }

  async getProduct(id: number): Promise<Product | undefined> {
    return this.products.get(id);
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    const id = this.productCurrentId++;
    const product: Product = { ...insertProduct, id };
    this.products.set(id, product);
    return product;
  }

  async deleteProduct(id: number): Promise<boolean> {
    return this.products.delete(id);
  }

  async bulkDeleteProducts(ids: number[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const id of ids) {
      if (this.products.delete(id)) {
        success++;
      } else {
        failed++;
      }
    }

    return { success, failed };
  }

  async createInvoice(insertInvoice: InsertInvoice): Promise<Invoice> {
    const id = this.invoiceCurrentId++;
    console.log('Creating invoice with data:', insertInvoice);
    const invoice: Invoice = {
      ...insertInvoice,
      id,
      isTrialOrder: insertInvoice.isTrialOrder || false,
      hasUpgraded: insertInvoice.hasUpgraded || false,
      upgradedAt: insertInvoice.upgradedAt || null
    };
    this.invoices.set(id, invoice);
    return invoice;
  }

  async getInvoice(id: number): Promise<Invoice | undefined> {
    return this.invoices.get(id);
  }

  async getInvoices(): Promise<Invoice[]> {
    return Array.from(this.invoices.values());
  }

  async updateInvoice(id: number, update: Partial<InsertInvoice>): Promise<Invoice | undefined> {
    const invoice = this.invoices.get(id);
    if (!invoice) return undefined;

    const updatedInvoice = { ...invoice, ...update };
    this.invoices.set(id, updatedInvoice);
    return updatedInvoice;
  }

  async deleteInvoice(id: number): Promise<boolean> {
    return this.invoices.delete(id);
  }

  async bulkDeleteInvoices(ids: number[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const id of ids) {
      if (this.invoices.delete(id)) {
        success++;
      } else {
        failed++;
      }
    }

    return { success, failed };
  }

  // Custom Checkout Page methods
  async createCustomCheckoutPage(insertPage: InsertCustomCheckoutPage): Promise<CustomCheckoutPage> {
    const id = this.customCheckoutPageCurrentId++;
    const page: CustomCheckoutPage = {
      ...insertPage,
      id,
      views: 0,
      conversions: 0
    };
    this.customCheckoutPages.set(id, page);
    return page;
  }

  async getCustomCheckoutPage(id: number): Promise<CustomCheckoutPage | undefined> {
    return this.customCheckoutPages.get(id);
  }

  async getCustomCheckoutPageBySlug(slug: string): Promise<CustomCheckoutPage | undefined> {
    return Array.from(this.customCheckoutPages.values()).find(
      (page) => page.slug === slug
    );
  }

  async getCustomCheckoutPages(): Promise<CustomCheckoutPage[]> {
    return Array.from(this.customCheckoutPages.values());
  }

  async updateCustomCheckoutPage(id: number, update: Partial<InsertCustomCheckoutPage>): Promise<CustomCheckoutPage | undefined> {
    const page = this.customCheckoutPages.get(id);
    if (!page) return undefined;

    const updatedPage = { ...page, ...update };
    this.customCheckoutPages.set(id, updatedPage);
    return updatedPage;
  }

  async incrementCustomCheckoutPageViews(id: number): Promise<void> {
    const page = this.customCheckoutPages.get(id);
    if (!page) return;

    page.views += 1;
    this.customCheckoutPages.set(id, page);
  }

  async incrementCustomCheckoutPageConversions(id: number): Promise<void> {
    const page = this.customCheckoutPages.get(id);
    if (!page) return;

    page.conversions += 1;
    this.customCheckoutPages.set(id, page);
  }

  async deleteCustomCheckoutPage(id: number): Promise<boolean> {
    return this.customCheckoutPages.delete(id);
  }

  private initializeProducts() {
    const products: InsertProduct[] = [
      {
        name: "Dashboard Pro Template",
        description: "Modern admin dashboard template with 50+ components, dark/light mode, and responsive design. Perfect for productivity apps and SaaS platforms.",
        price: "89.99",
        imageUrl: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: true
      },
      {
        name: "Task Management UI Kit",
        description: "Complete UI kit for task management applications with 100+ screens, components, and interactive prototypes for Figma.",
        price: "59.99",
        imageUrl: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: true
      },
      {
        name: "Mobile Productivity App Template",
        description: "React Native template for productivity apps with calendar, notes, tasks, and team collaboration features. iOS & Android ready.",
        price: "129.99",
        imageUrl: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: true
      },
      {
        name: "Design System Starter Kit",
        description: "Complete design system with 200+ components, design tokens, documentation, and code examples for React and Vue.js.",
        price: "149.99",
        imageUrl: "https://images.unsplash.com/photo-1561070791-2526d30994b5?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: true
      },
      {
        name: "Calendar & Scheduling Template",
        description: "Advanced calendar and scheduling template with booking system, time zones, recurring events, and team management features.",
        price: "79.99",
        imageUrl: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: true
      },
      {
        name: "Note-Taking App UI Kit",
        description: "Beautiful note-taking app interface with rich text editor, markdown support, tags, and collaborative features. Includes Figma files.",
        price: "49.99",
        imageUrl: "https://images.unsplash.com/photo-1586281380349-632531db7ed4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: true
      }
    ];

    products.forEach(product => {
      this.createProduct(product);
    });

    // No default allowed emails - user will add their own
  }

  // Allowed Email methods
  async getAllowedEmails(): Promise<AllowedEmail[]> {
    return Array.from(this.allowedEmails.values());
  }

  async getAllowedEmail(id: number): Promise<AllowedEmail | undefined> {
    return this.allowedEmails.get(id);
  }

  async getEmailByAddress(email: string): Promise<AllowedEmail | undefined> {
    // Case-insensitive check
    const normalizedEmail = email.toLowerCase();
    return Array.from(this.allowedEmails.values()).find(
      (allowedEmail) => allowedEmail.email.toLowerCase() === normalizedEmail
    );
  }

  async isEmailAllowed(email: string): Promise<boolean> {
    // Case-insensitive check
    const normalizedEmail = email.toLowerCase();
    return Array.from(this.allowedEmails.values()).some(
      (allowedEmail) => allowedEmail.email.toLowerCase() === normalizedEmail
    );
  }

  async createAllowedEmail(insertAllowedEmail: InsertAllowedEmail): Promise<AllowedEmail> {
    const id = this.allowedEmailCurrentId++;
    const allowedEmail: AllowedEmail = { ...insertAllowedEmail, id };
    this.allowedEmails.set(id, allowedEmail);
    return allowedEmail;
  }

  async updateAllowedEmail(id: number, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail | undefined> {
    const email = this.allowedEmails.get(id);
    if (!email) {
      return undefined;
    }
    const updatedEmail: AllowedEmail = { ...email, ...update };
    this.allowedEmails.set(id, updatedEmail);
    return updatedEmail;
  }

  async updateOrCreateAllowedEmail(emailAddress: string, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail> {
    // Find existing email (case-insensitive)
    const normalizedEmail = emailAddress.toLowerCase();
    const existingEmail = Array.from(this.allowedEmails.values()).find(
      (allowedEmail) => allowedEmail.email.toLowerCase() === normalizedEmail
    );

    if (existingEmail) {
      // Update existing email
      const updatedEmail: AllowedEmail = { ...existingEmail, ...update };
      this.allowedEmails.set(existingEmail.id, updatedEmail);
      return updatedEmail;
    } else {
      // Create new email
      return this.createAllowedEmail({
        email: emailAddress,
        notes: update.notes || '',
        smtpProvider: update.smtpProvider,
        lastUpdated: update.lastUpdated || new Date().toISOString(),
        createdAt: update.createdAt || new Date().toISOString()
      });
    }
  }

  async deleteAllowedEmail(id: number): Promise<boolean> {
    return this.allowedEmails.delete(id);
  }

  // Configuration methods
  async getGeneralSettings(): Promise<any> {
    // Return a mock general settings object
    return {
      siteName: "TemplateHub Pro",
      siteDescription: "Premium productivity app templates and UI/UX design systems",
      logoUrl: "",
      faviconUrl: "",
      primaryColor: "#6366f1",
      secondaryColor: "#4f46e5",
      footerText: "© 2024 TemplateHub Pro",
      enableCheckout: true,
      enableCustomCheckout: true,
      enableTestMode: true,
      defaultTestCustomer: {
        enabled: true,
        name: "Test Designer",
        email: "<EMAIL>"
      },
      emailDomainRestriction: {
        enabled: false,
        allowedDomains: "gmail.com, hotmail.com, yahoo.com"
      }
    };
  }

  async getEmailConfig(): Promise<any> {
    try {
      // Get SMTP providers from database
      const providers = await this.db.select().from(smtpProviders);

      // Transform database records to the expected format
      const transformedProviders = providers.map(provider => ({
        id: provider.id,
        name: provider.name,
        active: Boolean(provider.active),
        isDefault: Boolean(provider.isDefault),
        isBackup: Boolean(provider.isBackup),
        credentials: {
          host: provider.host,
          port: provider.port,
          secure: Boolean(provider.secure),
          auth: {
            user: provider.username,
            pass: provider.password
          },
          fromEmail: provider.fromEmail,
          fromName: provider.fromName
        }
      }));

      return {
        providers: transformedProviders
      };
    } catch (error) {
      console.error('Error fetching SMTP providers from database:', error);

      // Fallback to hardcoded config if database fails
      return {
        providers: [
          {
            id: 'smtp-1',
            name: 'Primary SMTP',
            active: true,
            isDefault: true,
            isBackup: false,
            credentials: {
              host: 'smtp-relay.brevo.com',
              port: '587',
              secure: false,
              auth: {
                user: '<EMAIL>',
                pass: '3d8I9xFm1yMDYj7W'
              },
              fromEmail: '<EMAIL>',
              fromName: 'PayPal Invoicer'
            }
          }
        ]
      };
    }
  }

  async saveSmtpProvider(provider: any): Promise<void> {
    try {
      const now = new Date().toISOString();

      // If this is set as default, unset all other defaults first
      if (provider.isDefault) {
        await this.db.update(smtpProviders)
          .set({ isDefault: 0, updatedAt: now })
          .where(sql`is_default = 1`);
      }

      // If this is set as backup, unset all other backups first
      if (provider.isBackup) {
        await this.db.update(smtpProviders)
          .set({ isBackup: 0, updatedAt: now })
          .where(sql`is_backup = 1`);
      }

      // Insert or update the provider
      await this.db.insert(smtpProviders).values({
        id: provider.id,
        name: provider.name,
        host: provider.host,
        port: provider.port,
        secure: provider.secure ? 1 : 0,
        username: provider.username,
        password: provider.password,
        fromEmail: provider.fromEmail,
        fromName: provider.fromName,
        active: provider.active ? 1 : 0,
        isDefault: provider.isDefault ? 1 : 0,
        isBackup: provider.isBackup ? 1 : 0,
        createdAt: now,
        updatedAt: now
      }).onConflictDoUpdate({
        target: smtpProviders.id,
        set: {
          name: provider.name,
          host: provider.host,
          port: provider.port,
          secure: provider.secure ? 1 : 0,
          username: provider.username,
          password: provider.password,
          fromEmail: provider.fromEmail,
          fromName: provider.fromName,
          active: provider.active ? 1 : 0,
          isDefault: provider.isDefault ? 1 : 0,
          isBackup: provider.isBackup ? 1 : 0,
          updatedAt: now
        }
      });

      console.log(`SMTP provider ${provider.name} saved successfully`);
    } catch (error) {
      console.error('Error saving SMTP provider:', error);
      throw error;
    }
  }

  async deleteSmtpProvider(providerId: string): Promise<void> {
    try {
      await this.db.delete(smtpProviders)
        .where(eq(smtpProviders.id, providerId));

      console.log(`SMTP provider ${providerId} deleted successfully`);
    } catch (error) {
      console.error('Error deleting SMTP provider:', error);
      throw error;
    }
  }

  async exportSmtpProviders(): Promise<any> {
    try {
      const providers = await this.db.select().from(smtpProviders);

      const exportData = {
        version: '1.0',
        exportDate: new Date().toISOString(),
        smtpProviders: providers.map(provider => ({
          id: provider.id,
          name: provider.name,
          host: provider.host,
          port: provider.port,
          secure: Boolean(provider.secure),
          username: provider.username,
          password: provider.password, // In production, consider encrypting this
          fromEmail: provider.fromEmail,
          fromName: provider.fromName,
          active: Boolean(provider.active),
          isDefault: Boolean(provider.isDefault),
          isBackup: Boolean(provider.isBackup),
          createdAt: provider.createdAt,
          updatedAt: provider.updatedAt
        }))
      };

      console.log(`Exported ${providers.length} SMTP providers`);
      return exportData;
    } catch (error) {
      console.error('Error exporting SMTP providers:', error);
      throw error;
    }
  }

  async importSmtpProviders(importData: any, options: { replaceExisting?: boolean, preserveIds?: boolean } = {}): Promise<{ imported: number, skipped: number, errors: string[] }> {
    try {
      const { replaceExisting = false, preserveIds = true } = options;
      const results = { imported: 0, skipped: 0, errors: [] as string[] };

      if (!importData.smtpProviders || !Array.isArray(importData.smtpProviders)) {
        throw new Error('Invalid import data: smtpProviders array not found');
      }

      // Get existing providers
      const existingProviders = await this.db.select().from(smtpProviders);
      const existingIds = new Set(existingProviders.map(p => p.id));

      for (const providerData of importData.smtpProviders) {
        try {
          const providerId = preserveIds ? providerData.id : `smtp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

          // Check if provider already exists
          if (existingIds.has(providerId)) {
            if (!replaceExisting) {
              results.skipped++;
              continue;
            }

            // Update existing provider
            await this.db.update(smtpProviders)
              .set({
                name: providerData.name,
                host: providerData.host,
                port: providerData.port,
                secure: providerData.secure ? 1 : 0,
                username: providerData.username,
                password: providerData.password,
                fromEmail: providerData.fromEmail,
                fromName: providerData.fromName,
                active: providerData.active ? 1 : 0,
                isDefault: providerData.isDefault ? 1 : 0,
                isBackup: providerData.isBackup ? 1 : 0,
                updatedAt: new Date().toISOString()
              })
              .where(eq(smtpProviders.id, providerId));
          } else {
            // Insert new provider
            const now = new Date().toISOString();
            await this.db.insert(smtpProviders).values({
              id: providerId,
              name: providerData.name,
              host: providerData.host,
              port: providerData.port,
              secure: providerData.secure ? 1 : 0,
              username: providerData.username,
              password: providerData.password,
              fromEmail: providerData.fromEmail,
              fromName: providerData.fromName,
              active: providerData.active ? 1 : 0,
              isDefault: providerData.isDefault ? 1 : 0,
              isBackup: providerData.isBackup ? 1 : 0,
              createdAt: providerData.createdAt || now,
              updatedAt: now
            });
          }

          results.imported++;
        } catch (error) {
          results.errors.push(`Failed to import provider ${providerData.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      console.log(`SMTP import completed: ${results.imported} imported, ${results.skipped} skipped, ${results.errors.length} errors`);
      return results;
    } catch (error) {
      console.error('Error importing SMTP providers:', error);
      throw error;
    }
  }

  async getPaymentConfig(): Promise<any> {
    // Return a mock payment config
    return {
      providers: [
        {
          id: 'paypal',
          name: 'PayPal',
          active: true,
          config: {
            clientId: '********',
            clientSecret: '********',
            mode: 'sandbox',
            webhookId: '',
            paypalEmail: '<EMAIL>'
          }
        },
        {
          id: 'custom-link',
          name: 'Custom Payment Links',
          active: true,
          config: {
            links: [
              {
                id: 'link-1',
                name: 'Default Payment Required',
                paymentLink: 'https://example.com/pay',
                buttonText: 'Complete Payment',
                successRedirectUrl: 'https://example.com/thank-you',
                active: true
              }
            ],
            rotationMethod: 'round-robin',
            lastUsedIndex: 0
          }
        }
      ]
    };
  }

  async bulkCreateAllowedEmails(emails: string[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const email of emails) {
      // Skip empty emails
      if (!email.trim()) {
        failed++;
        continue;
      }

      // Check if email already exists (case-insensitive)
      const normalizedEmail = email.trim().toLowerCase();
      const exists = Array.from(this.allowedEmails.values()).some(
        (allowedEmail) => allowedEmail.email.toLowerCase() === normalizedEmail
      );

      if (exists) {
        failed++;
        continue;
      }

      // Create the allowed email
      await this.createAllowedEmail({
        email: email.trim(),
        notes: "Bulk imported",
        smtpProvider: "",
        lastUpdated: new Date().toISOString(),
        createdAt: new Date().toISOString()
      });

      success++;
    }

    return { success, failed };
  }

  async bulkDeleteAllowedEmails(ids: number[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const id of ids) {
      if (this.allowedEmails.delete(id)) {
        success++;
      } else {
        failed++;
      }
    }

    return { success, failed };
  }

  // Email Template methods
  async getEmailTemplates(): Promise<EmailTemplate[]> {
    return Array.from(this.emailTemplates.values());
  }

  async getEmailTemplate(id: number): Promise<EmailTemplate | undefined> {
    return this.emailTemplates.get(id);
  }

  async createEmailTemplate(template: InsertEmailTemplate): Promise<EmailTemplate> {
    const id = this.emailTemplateCurrentId++;
    const emailTemplate: EmailTemplate = { ...template, id };
    this.emailTemplates.set(id, emailTemplate);
    return emailTemplate;
  }

  async updateEmailTemplate(id: number, update: Partial<InsertEmailTemplate>): Promise<EmailTemplate | undefined> {
    const template = this.emailTemplates.get(id);

    if (!template) {
      return undefined;
    }

    const updatedTemplate: EmailTemplate = { ...template, ...update };
    this.emailTemplates.set(id, updatedTemplate);
    return updatedTemplate;
  }

  async deleteEmailTemplate(id: number): Promise<boolean> {
    return this.emailTemplates.delete(id);
  }

  // PayPal Button methods
  async getPaypalButtons(): Promise<PaypalButton[]> {
    return Array.from(this.paypalButtons.values());
  }

  async getPaypalButton(id: number): Promise<PaypalButton | undefined> {
    return this.paypalButtons.get(id);
  }

  async createPaypalButton(button: InsertPaypalButton): Promise<PaypalButton> {
    const id = this.paypalButtonCurrentId++;
    const paypalButton: PaypalButton = { ...button, id };
    this.paypalButtons.set(id, paypalButton);
    return paypalButton;
  }

  async updatePaypalButton(id: number, update: Partial<InsertPaypalButton>): Promise<PaypalButton | undefined> {
    const button = this.paypalButtons.get(id);
    if (!button) return undefined;

    const updatedButton = { ...button, ...update };
    this.paypalButtons.set(id, updatedButton);
    return updatedButton;
  }

  async deletePaypalButton(id: number): Promise<boolean> {
    return this.paypalButtons.delete(id);
  }

  // Custom Invoice methods
  async getCustomInvoices(): Promise<CustomInvoice[]> {
    return Array.from(this.customInvoices.values());
  }

  async getCustomInvoice(id: number): Promise<CustomInvoice | undefined> {
    return this.customInvoices.get(id);
  }

  async getCustomInvoiceByNumber(invoiceNumber: string): Promise<CustomInvoice | undefined> {
    return Array.from(this.customInvoices.values()).find(
      (invoice) => invoice.invoiceNumber === invoiceNumber
    );
  }

  async createCustomInvoice(invoice: InsertCustomInvoice): Promise<CustomInvoice> {
    const id = this.customInvoiceCurrentId++;
    const customInvoice: CustomInvoice = {
      ...invoice,
      id,
      viewCount: 0,
      paidAt: null
    };
    this.customInvoices.set(id, customInvoice);
    return customInvoice;
  }

  async updateCustomInvoice(id: number, update: Partial<InsertCustomInvoice>): Promise<CustomInvoice | undefined> {
    const invoice = this.customInvoices.get(id);
    if (!invoice) return undefined;

    const updatedInvoice = { ...invoice, ...update };
    this.customInvoices.set(id, updatedInvoice);
    return updatedInvoice;
  }

  async incrementCustomInvoiceViewCount(id: number): Promise<void> {
    const invoice = this.customInvoices.get(id);
    if (!invoice) return;

    invoice.viewCount += 1;
    this.customInvoices.set(id, invoice);
  }

  async markCustomInvoiceAsPaid(id: number): Promise<CustomInvoice | undefined> {
    const invoice = this.customInvoices.get(id);
    if (!invoice) return undefined;

    const updatedInvoice = {
      ...invoice,
      status: 'paid',
      paidAt: new Date().toISOString()
    };
    this.customInvoices.set(id, updatedInvoice);
    return updatedInvoice;
  }

  async deleteCustomInvoice(id: number): Promise<boolean> {
    return this.customInvoices.delete(id);
  }







  // Initialize sample PayPal buttons
  private initializePaypalButtons() {
    const sampleButtons: InsertPaypalButton[] = [
      {
        name: "Basic PayPal Button",
        buttonCode: `<form action="https://www.paypal.com/cgi-bin/webscr" method="post" target="_top">
<input type="hidden" name="cmd" value="_s-xclick">
<input type="hidden" name="hosted_button_id" value="SAMPLE123456">
<input type="image" src="https://www.paypalobjects.com/en_US/i/btn/btn_buynowCC_LG.gif" border="0" name="submit" alt="PayPal - The safer, easier way to pay online!">
<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">
</form>`,
        description: "Standard PayPal Buy Now button",
        active: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        name: "Premium PayPal Button",
        buttonCode: `<form action="https://www.paypal.com/cgi-bin/webscr" method="post" target="_top">
<input type="hidden" name="cmd" value="_s-xclick">
<input type="hidden" name="hosted_button_id" value="PREMIUM789012">
<input type="image" src="https://www.paypalobjects.com/en_US/i/btn/btn_paynowCC_LG.gif" border="0" name="submit" alt="PayPal - The safer, easier way to pay online!">
<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">
</form>`,
        description: "Premium PayPal Pay Now button",
        active: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        name: "Subscription PayPal Button",
        buttonCode: `<form action="https://www.paypal.com/cgi-bin/webscr" method="post" target="_top">
<input type="hidden" name="cmd" value="_s-xclick">
<input type="hidden" name="hosted_button_id" value="SUB345678">
<input type="image" src="https://www.paypalobjects.com/en_US/i/btn/btn_subscribeCC_LG.gif" border="0" name="submit" alt="PayPal - The safer, easier way to pay online!">
<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">
</form>`,
        description: "PayPal Subscription button",
        active: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    sampleButtons.forEach(button => {
      this.createPaypalButton(button);
    });
  }

  // Contact Inquiry methods
  async createContactInquiry(inquiry: any): Promise<any> {
    const id = this.contactInquiryCurrentId++;
    const contactInquiry = {
      ...inquiry,
      id
    };
    this.contactInquiries.set(id, contactInquiry);
    return contactInquiry;
  }

  async getContactInquiries(): Promise<any[]> {
    return Array.from(this.contactInquiries.values()).sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }

  async updateContactInquiry(id: number, update: any): Promise<any> {
    const inquiry = this.contactInquiries.get(id);
    if (!inquiry) return undefined;

    const updatedInquiry = { ...inquiry, ...update };
    this.contactInquiries.set(id, updatedInquiry);
    return updatedInquiry;
  }

  // Embed Code methods
  async getEmbedCodes(): Promise<EmbedCode[]> {
    return Array.from(this.embedCodes.values());
  }

  async getEmbedCode(id: string): Promise<EmbedCode | undefined> {
    return this.embedCodes.get(id);
  }

  async createEmbedCode(embedCode: EmbedCode): Promise<EmbedCode> {
    this.embedCodes.set(embedCode.id, embedCode);
    return embedCode;
  }

  async updateEmbedCode(id: string, update: Partial<EmbedCode>): Promise<EmbedCode | undefined> {
    const embedCode = this.embedCodes.get(id);
    if (!embedCode) return undefined;

    const updatedEmbedCode = { ...embedCode, ...update };
    this.embedCodes.set(id, updatedEmbedCode);
    return updatedEmbedCode;
  }

  async deleteEmbedCode(id: string): Promise<boolean> {
    return this.embedCodes.delete(id);
  }
}

// Database Storage Implementation
export class DatabaseStorage implements IStorage {
  private db: typeof db;

  constructor() {
    this.db = db;
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    try {
      const result = await this.db.select().from(users).where(eq(users.id, id));
      return result[0];
    } catch (error) {
      console.error('Error getting user:', error);
      return undefined;
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    try {
      const result = await this.db.select().from(users).where(eq(users.username, username));
      return result[0];
    } catch (error) {
      console.error('Error getting user by username:', error);
      return undefined;
    }
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    // Note: The current schema doesn't have email field, so we'll return undefined
    // This can be extended when email field is added to users table
    return undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    try {
      // Hash the password if it's not already hashed
      let password = insertUser.password;
      if (!password.match(/^[0-9a-f]{64}$/i)) {
        password = createHash('sha256').update(password).digest('hex');
      }

      const userToInsert = {
        username: insertUser.username,
        password: password
      };

      const result = await this.db.insert(users).values(userToInsert).returning();
      return result[0] as User;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  async verifyUserCredentials(username: string, password: string): Promise<boolean> {
    const user = await this.getUserByUsername(username);
    if (!user) return false;

    // Hash the password for comparison
    const hashedPassword = createHash('sha256').update(password).digest('hex');
    return user.password === hashedPassword;
  }

  async verifyUserCredentialsWithUser(user: User, password: string): Promise<boolean> {
    if (!user) return false;

    // Hash the password for comparison
    const hashedPassword = createHash('sha256').update(password).digest('hex');
    return user.password === hashedPassword;
  }

  // For now, these methods will be no-ops since the current schema doesn't support them
  // They can be implemented when the schema is extended
  async saveResetToken(userId: number, token: string, expiry: Date): Promise<void> {
    // TODO: Implement when schema supports reset tokens
  }

  async validateResetToken(token: string): Promise<boolean> {
    // TODO: Implement when schema supports reset tokens
    return false;
  }

  async getUserByResetToken(token: string): Promise<User | undefined> {
    // TODO: Implement when schema supports reset tokens
    return undefined;
  }

  async updateUserPassword(userId: number, password: string): Promise<void> {
    try {
      const hashedPassword = createHash('sha256').update(password).digest('hex');
      await this.db.update(users).set({ password: hashedPassword }).where(eq(users.id, userId));
    } catch (error) {
      console.error('Error updating user password:', error);
    }
  }

  async clearResetToken(userId: number): Promise<void> {
    // TODO: Implement when schema supports reset tokens
  }

  async updateUsername(userId: number, username: string): Promise<void> {
    try {
      await this.db.update(users).set({ username }).where(eq(users.id, userId));
    } catch (error) {
      console.error('Error updating username:', error);
    }
  }

  async updateAutoLoginSettings(userId: number, rememberMe: boolean): Promise<void> {
    // TODO: Implement when schema supports rememberMe field
  }

  async enableTwoFactor(userId: number, secret: string): Promise<void> {
    // TODO: Implement when schema supports 2FA fields
  }

  async disableTwoFactor(userId: number): Promise<void> {
    // TODO: Implement when schema supports 2FA fields
  }

  async verifyTwoFactorToken(userId: number, token: string): Promise<boolean> {
    // TODO: Implement when schema supports 2FA fields
    return false;
  }

  async generateRecoveryCodes(userId: number): Promise<string[]> {
    // TODO: Implement when schema supports recovery codes
    return [];
  }

  async verifyRecoveryCode(userId: number, code: string): Promise<boolean> {
    // TODO: Implement when schema supports recovery codes
    return false;
  }

  async addDevice(userId: number, deviceInfo: Omit<Device, 'id' | 'createdAt' | 'lastLogin'>): Promise<Device> {
    // TODO: Implement when schema supports device tracking
    const device: Device = {
      ...deviceInfo,
      id: randomBytes(16).toString('hex'),
      createdAt: new Date().toISOString(),
      lastLogin: new Date().toISOString()
    };
    return device;
  }

  async getDevices(userId: number): Promise<Device[]> {
    // TODO: Implement when schema supports device tracking
    return [];
  }

  async updateDeviceLastLogin(userId: number, deviceId: string): Promise<void> {
    // TODO: Implement when schema supports device tracking
  }

  async removeDevice(userId: number, deviceId: string): Promise<boolean> {
    // TODO: Implement when schema supports device tracking
    return false;
  }

  // Product methods
  async getProducts(): Promise<Product[]> {
    try {
      const result = await this.db.select().from(products);
      return result;
    } catch (error) {
      console.error('Error getting products:', error);
      return [];
    }
  }

  async getProduct(id: number): Promise<Product | undefined> {
    try {
      const result = await this.db.select().from(products).where(eq(products.id, id));
      return result[0];
    } catch (error) {
      console.error('Error getting product:', error);
      return undefined;
    }
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    try {
      // Insert the product
      await this.db.insert(products).values(insertProduct);

      // Get the last inserted product by name (since MySQL doesn't support returning)
      const result = await this.db.select().from(products)
        .where(eq(products.name, insertProduct.name))
        .orderBy(sql`id DESC`)
        .limit(1);

      if (result.length === 0) {
        throw new Error('Failed to retrieve created product');
      }

      return result[0] as Product;
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  }

  async deleteProduct(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(products).where(eq(products.id, id));
      return true; // MySQL doesn't return affected rows count in this setup
    } catch (error) {
      console.error('Error deleting product:', error);
      return false;
    }
  }

  async bulkDeleteProducts(ids: number[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const id of ids) {
      try {
        await this.db.delete(products).where(eq(products.id, id));
        success++;
      } catch (error) {
        console.error(`Error deleting product ${id}:`, error);
        failed++;
      }
    }

    return { success, failed };
  }

  // Invoice methods
  async createInvoice(insertInvoice: InsertInvoice): Promise<Invoice> {
    try {
      console.log('Creating invoice with data:', insertInvoice);

      // Insert the invoice
      await this.db.insert(invoices).values(insertInvoice);

      // Get the last inserted invoice by using the paypalInvoiceId as unique identifier
      // Since MySQL doesn't support RETURNING, we'll use the unique paypalInvoiceId
      if (insertInvoice.paypalInvoiceId) {
        const result = await this.db.select().from(invoices)
          .where(eq(invoices.paypalInvoiceId, insertInvoice.paypalInvoiceId))
          .orderBy(sql`id DESC`)
          .limit(1);

        if (result.length > 0) {
          return result[0] as Invoice;
        }
      }

      // Fallback: get the latest invoice by email and amount
      if (insertInvoice.customerEmail && insertInvoice.amount) {
        const result = await this.db.select().from(invoices)
          .where(
            and(
              eq(invoices.customerEmail, insertInvoice.customerEmail),
              eq(invoices.amount, insertInvoice.amount)
            )
          )
          .orderBy(sql`id DESC`)
          .limit(1);

        if (result.length > 0) {
          return result[0] as Invoice;
        }
      }

      // Final fallback: get the very latest invoice
      const result = await this.db.select().from(invoices)
        .orderBy(sql`id DESC`)
        .limit(1);

      if (result.length === 0) {
        throw new Error('Failed to retrieve created invoice');
      }

      return result[0] as Invoice;
    } catch (error) {
      console.error('Error creating invoice:', error);
      throw error;
    }
  }

  async getInvoice(id: number): Promise<Invoice | undefined> {
    try {
      const result = await this.db.select().from(invoices).where(eq(invoices.id, id));
      return result[0];
    } catch (error) {
      console.error('Error getting invoice:', error);
      return undefined;
    }
  }

  async getInvoices(): Promise<Invoice[]> {
    try {
      const result = await this.db.select().from(invoices);
      return result;
    } catch (error) {
      console.error('Error getting invoices:', error);
      return [];
    }
  }

  async updateInvoice(id: number, update: Partial<InsertInvoice>): Promise<Invoice | undefined> {
    try {
      // Update the invoice
      await this.db.update(invoices).set(update).where(eq(invoices.id, id));

      // Get the updated invoice
      const result = await this.db.select().from(invoices).where(eq(invoices.id, id));
      return result[0] as Invoice;
    } catch (error) {
      console.error('Error updating invoice:', error);
      return undefined;
    }
  }

  async deleteInvoice(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(invoices).where(eq(invoices.id, id));
      return true; // MySQL doesn't return affected rows count in this setup
    } catch (error) {
      console.error('Error deleting invoice:', error);
      return false;
    }
  }

  async bulkDeleteInvoices(ids: number[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const id of ids) {
      try {
        await this.db.delete(invoices).where(eq(invoices.id, id));
        success++;
      } catch (error) {
        console.error(`Error deleting invoice ${id}:`, error);
        failed++;
      }
    }

    return { success, failed };
  }

  // Configuration methods - delegate to MemStorage for now
  async getGeneralSettings(): Promise<any> {
    return memStorage.getGeneralSettings();
  }

  async getEmailConfig(): Promise<any> {
    try {
      // Get SMTP providers from database
      const providers = await this.db.select().from(smtpProviders);

      // Transform database records to the expected format
      const transformedProviders = providers.map(provider => ({
        id: provider.id,
        name: provider.name,
        active: Boolean(provider.active),
        isDefault: Boolean(provider.isDefault),
        isBackup: Boolean(provider.isBackup),
        credentials: {
          host: provider.host,
          port: provider.port,
          secure: Boolean(provider.secure),
          auth: {
            user: provider.username,
            pass: provider.password
          },
          fromEmail: provider.fromEmail,
          fromName: provider.fromName
        }
      }));

      return {
        providers: transformedProviders
      };
    } catch (error) {
      console.error('Error fetching SMTP providers from database:', error);

      // Fallback to hardcoded config if database fails
      return {
        providers: [
          {
            id: 'smtp-1',
            name: 'Primary SMTP',
            active: true,
            isDefault: true,
            isBackup: false,
            credentials: {
              host: 'smtp-relay.brevo.com',
              port: '587',
              secure: false,
              auth: {
                user: '<EMAIL>',
                pass: '3d8I9xFm1yMDYj7W'
              },
              fromEmail: '<EMAIL>',
              fromName: 'PayPal Invoicer'
            }
          }
        ]
      };
    }
  }

  async createSmtpProvider(provider: any): Promise<any> {
    try {
      const result = await this.db.insert(smtpProviders).values({
        id: provider.id,
        name: provider.name,
        host: provider.host,
        port: provider.port,
        secure: provider.secure,
        username: provider.username,
        password: provider.password,
        fromEmail: provider.fromEmail,
        fromName: provider.fromName,
        active: provider.active,
        isDefault: provider.isDefault,
        isBackup: provider.isBackup,
        createdAt: provider.createdAt,
        updatedAt: provider.updatedAt
      });

      console.log(`✅ SMTP provider created in database: ${provider.id}`);
      return result;
    } catch (error) {
      console.error('Error creating SMTP provider:', error);
      throw error;
    }
  }



  async saveSmtpProvider(provider: any): Promise<void> {
    try {
      const now = new Date().toISOString();

      // If this is set as default, unset all other defaults first
      if (provider.isDefault) {
        await this.db.update(smtpProviders)
          .set({ isDefault: false, updatedAt: now })
          .where(sql`is_default = 1`);
      }

      // If this is set as backup, unset all other backups first
      if (provider.isBackup) {
        await this.db.update(smtpProviders)
          .set({ isBackup: false, updatedAt: now })
          .where(sql`is_backup = 1`);
      }

      // Check if provider exists
      const existingProvider = await this.db.select().from(smtpProviders).where(eq(smtpProviders.id, provider.id));

      if (existingProvider.length > 0) {
        // Update existing provider
        await this.db.update(smtpProviders)
          .set({
            name: provider.name,
            host: provider.host,
            port: provider.port,
            secure: provider.secure ? true : false,
            username: provider.username,
            password: provider.password,
            fromEmail: provider.fromEmail,
            fromName: provider.fromName,
            active: provider.active ? true : false,
            isDefault: provider.isDefault ? true : false,
            isBackup: provider.isBackup ? true : false,
            updatedAt: now
          })
          .where(eq(smtpProviders.id, provider.id));
      } else {
        // Insert new provider
        await this.db.insert(smtpProviders).values({
          id: provider.id,
          name: provider.name,
          host: provider.host,
          port: provider.port,
          secure: provider.secure ? true : false,
          username: provider.username,
          password: provider.password,
          fromEmail: provider.fromEmail,
          fromName: provider.fromName,
          active: provider.active ? true : false,
          isDefault: provider.isDefault ? true : false,
          isBackup: provider.isBackup ? true : false,
          createdAt: now,
          updatedAt: now
        });
      }

      console.log(`SMTP provider ${provider.id} saved successfully`);
    } catch (error) {
      console.error('Error saving SMTP provider:', error);
      throw error;
    }
  }

  async exportSmtpProviders(): Promise<any> {
    try {
      const providers = await this.db.select().from(smtpProviders);

      const exportData = {
        exportDate: new Date().toISOString(),
        version: '1.0',
        smtpProviders: providers.map(provider => ({
          id: provider.id,
          name: provider.name,
          host: provider.host,
          port: provider.port,
          secure: Boolean(provider.secure),
          username: provider.username,
          password: provider.password,
          fromEmail: provider.fromEmail,
          fromName: provider.fromName,
          active: Boolean(provider.active),
          isDefault: Boolean(provider.isDefault),
          isBackup: Boolean(provider.isBackup),
          createdAt: provider.createdAt,
          updatedAt: provider.updatedAt
        }))
      };

      console.log(`Exported ${providers.length} SMTP providers`);
      return exportData;
    } catch (error) {
      console.error('Error exporting SMTP providers:', error);
      throw error;
    }
  }

  async importSmtpProviders(importData: any, options: { replaceExisting?: boolean, preserveIds?: boolean } = {}): Promise<{ imported: number, skipped: number, errors: string[] }> {
    try {
      const { replaceExisting = false, preserveIds = true } = options;
      const results = { imported: 0, skipped: 0, errors: [] as string[] };

      if (!importData.smtpProviders || !Array.isArray(importData.smtpProviders)) {
        throw new Error('Invalid import data: smtpProviders array not found');
      }

      // Get existing providers
      const existingProviders = await this.db.select().from(smtpProviders);
      const existingIds = new Set(existingProviders.map(p => p.id));

      for (const providerData of importData.smtpProviders) {
        try {
          const providerId = preserveIds ? providerData.id : `smtp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

          // Check if provider already exists
          if (existingIds.has(providerId)) {
            if (!replaceExisting) {
              results.skipped++;
              continue;
            }

            // Update existing provider
            const now = new Date().toISOString();
            await this.db.update(smtpProviders)
              .set({
                name: providerData.name,
                host: providerData.host,
                port: providerData.port,
                secure: providerData.secure ? 1 : 0,
                username: providerData.username,
                password: providerData.password,
                fromEmail: providerData.fromEmail,
                fromName: providerData.fromName,
                active: providerData.active ? 1 : 0,
                isDefault: providerData.isDefault ? 1 : 0,
                isBackup: providerData.isBackup ? 1 : 0,
                updatedAt: now
              })
              .where(eq(smtpProviders.id, providerId));
          } else {
            // Insert new provider
            const now = new Date().toISOString();
            await this.db.insert(smtpProviders).values({
              id: providerId,
              name: providerData.name,
              host: providerData.host,
              port: providerData.port,
              secure: providerData.secure ? 1 : 0,
              username: providerData.username,
              password: providerData.password,
              fromEmail: providerData.fromEmail,
              fromName: providerData.fromName,
              active: providerData.active ? 1 : 0,
              isDefault: providerData.isDefault ? 1 : 0,
              isBackup: providerData.isBackup ? 1 : 0,
              createdAt: providerData.createdAt || now,
              updatedAt: now
            });
          }

          results.imported++;
        } catch (error) {
          results.errors.push(`Failed to import provider ${providerData.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      console.log(`SMTP import completed: ${results.imported} imported, ${results.skipped} skipped, ${results.errors.length} errors`);
      return results;
    } catch (error) {
      console.error('Error importing SMTP providers:', error);
      throw error;
    }
  }

  async getPaymentConfig(): Promise<any> {
    return memStorage.getPaymentConfig();
  }

  // Custom Checkout Pages - MySQL Implementation
  async createCustomCheckoutPage(page: InsertCustomCheckoutPage): Promise<CustomCheckoutPage> {
    try {
      const [result] = await this.db.insert(customCheckoutPages).values(page).returning();
      return result;
    } catch (error) {
      console.error('Error creating custom checkout page:', error);
      throw error;
    }
  }

  async getCustomCheckoutPage(id: number): Promise<CustomCheckoutPage | undefined> {
    try {
      const result = await this.db.select().from(customCheckoutPages).where(eq(customCheckoutPages.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting custom checkout page:', error);
      return undefined;
    }
  }

  async getCustomCheckoutPageBySlug(slug: string): Promise<CustomCheckoutPage | undefined> {
    try {
      const result = await this.db.select().from(customCheckoutPages).where(eq(customCheckoutPages.slug, slug)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting custom checkout page by slug:', error);
      return undefined;
    }
  }

  async getCustomCheckoutPages(): Promise<CustomCheckoutPage[]> {
    try {
      const result = await this.db.select().from(customCheckoutPages).orderBy(customCheckoutPages.createdAt);
      return result;
    } catch (error) {
      console.error('Error getting custom checkout pages:', error);
      return [];
    }
  }

  async updateCustomCheckoutPage(id: number, update: Partial<InsertCustomCheckoutPage>): Promise<CustomCheckoutPage | undefined> {
    try {
      const [result] = await this.db.update(customCheckoutPages).set(update).where(eq(customCheckoutPages.id, id)).returning();
      return result;
    } catch (error) {
      console.error('Error updating custom checkout page:', error);
      return undefined;
    }
  }

  async incrementCustomCheckoutPageViews(id: number): Promise<void> {
    try {
      await this.db.update(customCheckoutPages)
        .set({ views: sql`${customCheckoutPages.views} + 1` })
        .where(eq(customCheckoutPages.id, id));
    } catch (error) {
      console.error('Error incrementing custom checkout page views:', error);
    }
  }

  async incrementCustomCheckoutPageConversions(id: number): Promise<void> {
    try {
      await this.db.update(customCheckoutPages)
        .set({ conversions: sql`${customCheckoutPages.conversions} + 1` })
        .where(eq(customCheckoutPages.id, id));
    } catch (error) {
      console.error('Error incrementing custom checkout page conversions:', error);
    }
  }

  async deleteCustomCheckoutPage(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(customCheckoutPages).where(eq(customCheckoutPages.id, id));
      return result.rowsAffected > 0;
    } catch (error) {
      console.error('Error deleting custom checkout page:', error);
      return false;
    }
  }

  // Allowed Emails - MySQL Implementation
  async getAllowedEmails(): Promise<AllowedEmail[]> {
    try {
      const result = await this.db.select().from(allowedEmails).orderBy(allowedEmails.createdAt);
      return result;
    } catch (error) {
      console.error('Error getting allowed emails:', error);
      return [];
    }
  }

  async getAllowedEmail(id: number): Promise<AllowedEmail | undefined> {
    try {
      const result = await this.db.select().from(allowedEmails).where(eq(allowedEmails.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting allowed email:', error);
      return undefined;
    }
  }

  async getEmailByAddress(email: string): Promise<AllowedEmail | undefined> {
    try {
      const result = await this.db.select().from(allowedEmails).where(eq(allowedEmails.email, email)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting email by address:', error);
      return undefined;
    }
  }

  async isEmailAllowed(email: string): Promise<boolean> {
    try {
      const result = await this.db.select().from(allowedEmails).where(eq(allowedEmails.email, email)).limit(1);
      return result.length > 0;
    } catch (error) {
      console.error('Error checking if email is allowed:', error);
      return false;
    }
  }

  async createAllowedEmail(email: InsertAllowedEmail): Promise<AllowedEmail> {
    try {
      const [result] = await this.db.insert(allowedEmails).values(email).returning();
      return result;
    } catch (error) {
      console.error('Error creating allowed email:', error);
      throw error;
    }
  }

  async updateAllowedEmail(id: number, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail | undefined> {
    try {
      const [result] = await this.db.update(allowedEmails).set(update).where(eq(allowedEmails.id, id)).returning();
      return result;
    } catch (error) {
      console.error('Error updating allowed email:', error);
      return undefined;
    }
  }

  async updateOrCreateAllowedEmail(emailAddress: string, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail> {
    try {
      const existing = await this.getEmailByAddress(emailAddress);
      if (existing) {
        const updated = await this.updateAllowedEmail(existing.id, update);
        return updated || existing;
      } else {
        return await this.createAllowedEmail({ ...update, email: emailAddress } as InsertAllowedEmail);
      }
    } catch (error) {
      console.error('Error updating or creating allowed email:', error);
      throw error;
    }
  }

  async deleteAllowedEmail(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(allowedEmails).where(eq(allowedEmails.id, id));
      return result.rowsAffected > 0;
    } catch (error) {
      console.error('Error deleting allowed email:', error);
      return false;
    }
  }

  async bulkCreateAllowedEmails(emails: string[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const email of emails) {
      try {
        await this.createAllowedEmail({
          email,
          notes: 'Bulk imported',
          smtpProvider: 'avixiptv-smtp',
          lastUpdated: new Date().toISOString(),
          createdAt: new Date().toISOString()
        });
        success++;
      } catch (error) {
        failed++;
      }
    }

    return { success, failed };
  }

  async bulkDeleteAllowedEmails(ids: number[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const id of ids) {
      try {
        const deleted = await this.deleteAllowedEmail(id);
        if (deleted) success++;
        else failed++;
      } catch (error) {
        failed++;
      }
    }

    return { success, failed };
  }

  // Email Templates - MySQL Implementation
  async getEmailTemplates(): Promise<EmailTemplate[]> {
    try {
      const result = await this.db.select().from(emailTemplates).orderBy(emailTemplates.createdAt);
      return result;
    } catch (error) {
      console.error('Error getting email templates:', error);
      return [];
    }
  }

  async getEmailTemplate(id: number): Promise<EmailTemplate | undefined> {
    try {
      const result = await this.db.select().from(emailTemplates).where(eq(emailTemplates.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting email template:', error);
      return undefined;
    }
  }

  async createEmailTemplate(template: InsertEmailTemplate): Promise<EmailTemplate> {
    try {
      const [result] = await this.db.insert(emailTemplates).values(template).returning();
      return result;
    } catch (error) {
      console.error('Error creating email template:', error);
      throw error;
    }
  }

  async updateEmailTemplate(id: number, update: Partial<InsertEmailTemplate>): Promise<EmailTemplate | undefined> {
    try {
      const [result] = await this.db.update(emailTemplates).set(update).where(eq(emailTemplates.id, id)).returning();
      return result;
    } catch (error) {
      console.error('Error updating email template:', error);
      return undefined;
    }
  }

  async deleteEmailTemplate(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(emailTemplates).where(eq(emailTemplates.id, id));
      return result.rowsAffected > 0;
    } catch (error) {
      console.error('Error deleting email template:', error);
      return false;
    }
  }

  async getPaypalButtons(): Promise<PaypalButton[]> {
    return memStorage.getPaypalButtons();
  }

  async getPaypalButton(id: number): Promise<PaypalButton | undefined> {
    return memStorage.getPaypalButton(id);
  }

  async createPaypalButton(button: InsertPaypalButton): Promise<PaypalButton> {
    return memStorage.createPaypalButton(button);
  }

  async updatePaypalButton(id: number, update: Partial<InsertPaypalButton>): Promise<PaypalButton | undefined> {
    return memStorage.updatePaypalButton(id, update);
  }

  async deletePaypalButton(id: number): Promise<boolean> {
    return memStorage.deletePaypalButton(id);
  }

  async getCustomInvoices(): Promise<CustomInvoice[]> {
    return memStorage.getCustomInvoices();
  }

  async getCustomInvoice(id: number): Promise<CustomInvoice | undefined> {
    return memStorage.getCustomInvoice(id);
  }

  async getCustomInvoiceByNumber(invoiceNumber: string): Promise<CustomInvoice | undefined> {
    return memStorage.getCustomInvoiceByNumber(invoiceNumber);
  }

  async createCustomInvoice(invoice: InsertCustomInvoice): Promise<CustomInvoice> {
    return memStorage.createCustomInvoice(invoice);
  }

  async updateCustomInvoice(id: number, update: Partial<InsertCustomInvoice>): Promise<CustomInvoice | undefined> {
    return memStorage.updateCustomInvoice(id, update);
  }

  async incrementCustomInvoiceViewCount(id: number): Promise<void> {
    return memStorage.incrementCustomInvoiceViewCount(id);
  }

  async markCustomInvoiceAsPaid(id: number): Promise<CustomInvoice | undefined> {
    return memStorage.markCustomInvoiceAsPaid(id);
  }

  async deleteCustomInvoice(id: number): Promise<boolean> {
    return memStorage.deleteCustomInvoice(id);
  }

  async createContactInquiry?(inquiry: any): Promise<any> {
    return memStorage.createContactInquiry?.(inquiry);
  }

  async getContactInquiries?(): Promise<any[]> {
    return memStorage.getContactInquiries?.() || [];
  }

  async updateContactInquiry?(id: number, update: any): Promise<any> {
    return memStorage.updateContactInquiry?.(id, update);
  }

  async getEmbedCodes(): Promise<EmbedCode[]> {
    return memStorage.getEmbedCodes();
  }

  async getEmbedCode(id: string): Promise<EmbedCode | undefined> {
    return memStorage.getEmbedCode(id);
  }

  async createEmbedCode(embedCode: EmbedCode): Promise<EmbedCode> {
    return memStorage.createEmbedCode(embedCode);
  }

  async updateEmbedCode(id: string, update: Partial<EmbedCode>): Promise<EmbedCode | undefined> {
    return memStorage.updateEmbedCode(id, update);
  }

  async deleteEmbedCode(id: string): Promise<boolean> {
    return memStorage.deleteEmbedCode(id);
  }
}

// Create instances
const memStorage = new MemStorage();
export const storage = new DatabaseStorage();
