{"timestamp": "2025-06-02T19:07:58.109Z", "database": "mysql://***:***@localhost:3306/db", "emailTemplates": [{"template_id": "avix-template", "name": "AVIX", "description": "AVIX email template for IPTV subscriptions", "subject": "Your AVIX IPTV Subscription Details", "html_content": "<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\"><h2 style=\"color: #333;\">Your AVIX IPTV Subscription</h2><p>Dear {{customerName}},</p><p>Thank you for subscribing to AVIX IPTV! Your account is ready to use.</p><div style=\"background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;\"><h3>Login Credentials:</h3><p><strong>Username:</strong> {{username}}<br><strong>Password:</strong> {{password}}</p><p><strong>M3U URL:</strong><br>{{m3uUrl}}</p></div><p>Best regards,<br>AVIX Team</p></div>", "text_content": "Your AVIX IPTV Subscription\n\nDear {{customerName}},\n\nThank you for subscribing to AVIX IPTV! Your account is ready to use.\n\nUsername: {{username}}\nPassword: {{password}}\nM3U URL: {{m3uUrl}}\n\nBest regards,\nAVIX Team", "category": "subscription", "is_default": 0, "created_at": "2025-06-02T19:04:29.039Z", "updated_at": "2025-06-02T19:04:29.039Z"}, {"template_id": "directo-template", "name": "Directo", "description": "Directo email template for IPTV subscriptions", "subject": "Your Directo IPTV Subscription Details", "html_content": "<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\"><h2 style=\"color: #333;\">Your Directo IPTV Subscription</h2><p>Dear {{customerName}},</p><p>Welcome to Directo IPTV! Your subscription is now active.</p><div style=\"background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;\"><h3>Access Information:</h3><p><strong>Username:</strong> {{username}}<br><strong>Password:</strong> {{password}}</p><p><strong>M3U URL:</strong><br>{{m3uUrl}}</p></div><p>Enjoy your service!<br>Directo Team</p></div>", "text_content": "Your Directo IPTV Subscription\n\nDear {{customerName}},\n\nWelcome to Directo IPTV! Your subscription is now active.\n\nUsername: {{username}}\nPassword: {{password}}\nM3U URL: {{m3uUrl}}\n\nEnjoy your service!\nDirecto Team", "category": "subscription", "is_default": 0, "created_at": "2025-06-02T19:04:29.039Z", "updated_at": "2025-06-02T19:04:29.039Z"}, {"template_id": "smartonn-template", "name": "Smartonn", "description": "Smartonn email template for IPTV subscriptions", "subject": "Your Smartonn IPTV Subscription Details", "html_content": "<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\"><h2 style=\"color: #333;\">Your Smartonn IPTV Subscription</h2><p>Dear {{customerName}},</p><p>Welcome to Smartonn IPTV! Your premium subscription is now active.</p><div style=\"background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;\"><h3>Account Details:</h3><p><strong>Username:</strong> {{username}}<br><strong>Password:</strong> {{password}}</p><p><strong>M3U URL:</strong><br>{{m3uUrl}}</p></div><p>Thank you for choosing Smartonn!<br>Smartonn Support Team</p></div>", "text_content": "Your Smartonn IPTV Subscription\n\nDear {{customerName}},\n\nWelcome to Smartonn IPTV! Your premium subscription is now active.\n\nUsername: {{username}}\nPassword: {{password}}\nM3U URL: {{m3uUrl}}\n\nThank you for choosing Smartonn!\nSmartonn Support Team", "category": "subscription", "is_default": 1, "created_at": "2025-06-02T19:04:29.039Z", "updated_at": "2025-06-02T19:04:29.039Z"}, {"template_id": "tvzyon-template", "name": "TVZYON", "description": "TVZYON email template for IPTV subscriptions", "subject": "Your TVZYON IPTV Subscription Details", "html_content": "<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\"><h2 style=\"color: #333;\">Your TVZYON IPTV Subscription</h2><p>Dear {{customerName}},</p><p>Thank you for choosing TVZYON! Your IPTV subscription is ready.</p><div style=\"background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;\"><h3>Subscription Details:</h3><p><strong>Username:</strong> {{username}}<br><strong>Password:</strong> {{password}}</p><p><strong>M3U URL:</strong><br>{{m3uUrl}}</p></div><p>Best regards,<br>TVZYON Team</p></div>", "text_content": "Your TVZYON IPTV Subscription\n\nDear {{customerName}},\n\nThank you for choosing TVZYON! Your IPTV subscription is ready.\n\nUsername: {{username}}\nPassword: {{password}}\nM3U URL: {{m3uUrl}}\n\nBest regards,\nTVZYON Team", "category": "subscription", "is_default": 0, "created_at": "2025-06-02T19:04:29.039Z", "updated_at": "2025-06-02T19:04:29.039Z"}], "checkoutPages": [{"id": 1, "title": "Smartonn TEST", "slug": "smartonn-test", "product_name": "IPTV 24 Hours Trial", "product_description": "Experience premium IPTV service with our 24-hour trial. Access thousands of channels worldwide including sports, movies, TV shows, and more. Perfect for testing our service quality before committing to a full subscription.", "price": "10.00", "image_url": "/uploads/IPTV-24h.jpg", "payment_method": "custom-link", "custom_payment_link_id": "storazo-link", "paypal_button_id": "", "embed_code_id": "", "require_allowed_email": 1, "is_trial_checkout": 1, "confirmation_message": "<div class=\"space-y-3\"><p><strong>🎯 Ready to start your trial?</strong></p><p>✅ <strong>What you're getting:</strong></p><ul class=\"list-disc list-inside space-y-1\"><li>24 hours of premium IPTV access</li><li>Thousands of channels worldwide</li><li>HD/4K quality streaming</li><li>All device compatibility</li><li>Instant activation</li><li>Full customer support</li></ul><p class=\"text-sm text-muted-foreground mt-3\">💳 <em>Secure payment processing via Storazo</em></p><p class=\"text-xs text-muted-foreground\">After payment, you will receive your trial credentials within minutes.</p></div>", "header_title": "Smartonn IPTV Trial", "footer_text": "Secure payment • Instant delivery • 24/7 support", "header_logo": "", "footer_logo": "", "theme_mode": "light", "use_referrer_masking": 0, "redirect_delay": 2000, "expires_at": null, "active": 1, "views": 0, "conversions": 0, "created_at": "2025-06-02T19:05:26.162Z", "updated_at": "2025-06-02T19:05:26.163Z"}], "paymentLinks": [{"id": "billgang-link", "name": "<PERSON><PERSON><PERSON>", "url": "https://shippz.bgng.io/product/starter-access", "button_text": "Complete Payment", "success_redirect_url": "", "active": 1, "is_trial": 1, "created_at": "2025-06-02T19:04:29.093Z", "updated_at": "2025-06-02T19:04:29.093Z"}, {"id": "storazo-link", "name": "<PERSON><PERSON><PERSON>", "url": "https://storazo.com/checkout/?add-to-cart=893", "button_text": "Complete Payment", "success_redirect_url": "", "active": 1, "is_trial": 0, "created_at": "2025-06-02T19:04:29.093Z", "updated_at": "2025-06-02T19:04:29.093Z"}], "smtpProviders": [{"id": "avixiptv-smtp", "name": "AVIXIPTV", "host": "mail.avixiptv.com", "port": "587", "secure": 0, "username": "<EMAIL>", "password": "Avix@2024", "from_email": "<EMAIL>", "from_name": "AVIXIPTV Support", "active": 1, "is_default": 1, "is_backup": 0, "created_at": "2025-06-01T18:30:35.034Z", "updated_at": "2025-06-02T18:36:44.668Z"}, {"id": "directoiptv-smtp", "name": "DirectoIPTV", "host": "mail.directoiptv.com", "port": "587", "secure": 0, "username": "<EMAIL>", "password": "Directo@2024", "from_email": "<EMAIL>", "from_name": "DirectoIPTV Support", "active": 1, "is_default": 0, "is_backup": 1, "created_at": "2025-06-01T18:30:35.034Z", "updated_at": "2025-06-02T18:36:44.668Z"}, {"id": "enzidswan-smtp", "name": "Enzidswan", "host": "mail.enzidswan.com", "port": "587", "secure": 0, "username": "<EMAIL>", "password": "Enzid@2024", "from_email": "<EMAIL>", "from_name": "Enzidswan Support", "active": 1, "is_default": 0, "is_backup": 0, "created_at": "2025-06-01T18:30:35.034Z", "updated_at": "2025-06-02T18:36:44.668Z"}, {"id": "smartonn-smtp", "name": "Smartonn", "host": "mail.smartonn.com", "port": "587", "secure": 0, "username": "<EMAIL>", "password": "Smart@2024", "from_email": "<EMAIL>", "from_name": "Smartonn Support", "active": 1, "is_default": 0, "is_backup": 0, "created_at": "2025-06-01T18:30:35.034Z", "updated_at": "2025-06-02T18:36:44.668Z"}, {"id": "tvzyon-smtp", "name": "TVZYON", "host": "mail.tvzyon.com", "port": "587", "secure": 0, "username": "<EMAIL>", "password": "Tvzyon@2024", "from_email": "<EMAIL>", "from_name": "TVZYON Support", "active": 1, "is_default": 0, "is_backup": 0, "created_at": "2025-06-01T18:30:35.034Z", "updated_at": "2025-06-02T18:36:44.668Z"}], "allowedEmails": [{"id": 1, "email": "<EMAIL>", "notes": "Default admin email - <PERSON>", "smtp_provider": "avixiptv-smtp", "last_updated": "2025-06-02T19:04:29.105Z", "created_at": "2025-06-02T18:36:44.668Z"}, {"id": 3, "email": "<EMAIL>", "notes": "Storazo contact email", "smtp_provider": "avixiptv-smtp", "last_updated": "2025-06-02T19:04:29.105Z", "created_at": "2025-06-02T19:04:29.105Z"}]}