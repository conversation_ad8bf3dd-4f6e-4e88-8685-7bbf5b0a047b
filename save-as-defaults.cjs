const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
if (process.env.NODE_ENV === 'production') {
  dotenv.config({ path: '.env.production', override: true });
} else {
  dotenv.config();
}

const DATABASE_URL = process.env.DATABASE_URL || 'mysql://user:XwB8rMNKB3OZzBzyYZft@localhost:3306/db';

// Parse MySQL connection string
const url = new URL(DATABASE_URL);
const connectionConfig = {
  host: url.hostname,
  port: url.port || 3306,
  user: url.username,
  password: url.password,
  database: url.pathname.slice(1), // Remove leading slash
};

async function saveAsDefaults() {
  let connection;
  try {
    connection = await mysql.createConnection(connectionConfig);
    console.log('✅ Connected to MySQL database');

    console.log('\n💾 SAVING CURRENT CONFIGURATIONS AS DEFAULTS...');
    console.log('=' .repeat(60));

    // 1. Export Email Templates
    console.log('\n📝 Exporting Email Templates...');
    const [emailTemplates] = await connection.execute(`
      SELECT template_id, name, description, subject, html_content, text_content, 
             category, is_default, created_at, updated_at
      FROM email_templates 
      ORDER BY name
    `);

    console.log(`Found ${emailTemplates.length} email templates:`);
    emailTemplates.forEach((template, index) => {
      console.log(`${index + 1}. ${template.name} (${template.template_id})`);
    });

    // 2. Export Custom Checkout Pages
    console.log('\n🛒 Exporting Custom Checkout Pages...');
    const [checkoutPages] = await connection.execute(`
      SELECT * FROM custom_checkout_pages ORDER BY created_at
    `);

    console.log(`Found ${checkoutPages.length} checkout pages:`);
    checkoutPages.forEach((page, index) => {
      console.log(`${index + 1}. ${page.title} (${page.slug}) - $${page.price}`);
    });

    // 3. Export Payment Gateway URLs
    console.log('\n💳 Exporting Payment Gateway URLs...');
    const [paymentLinks] = await connection.execute(`
      SELECT * FROM custom_payment_links ORDER BY name
    `);

    console.log(`Found ${paymentLinks.length} payment links:`);
    paymentLinks.forEach((link, index) => {
      console.log(`${index + 1}. ${link.name}: ${link.url}`);
    });

    // 4. Export SMTP Providers
    console.log('\n📧 Exporting SMTP Providers...');
    const [smtpProviders] = await connection.execute(`
      SELECT * FROM smtp_providers ORDER BY is_default DESC, name
    `);

    console.log(`Found ${smtpProviders.length} SMTP providers:`);
    smtpProviders.forEach((provider, index) => {
      const status = provider.is_default ? '(Default)' : provider.is_backup ? '(Backup)' : '';
      console.log(`${index + 1}. ${provider.name} ${status}`);
    });

    // 5. Export Allowed Emails
    console.log('\n📧 Exporting Allowed Emails...');
    const [allowedEmails] = await connection.execute(`
      SELECT * FROM allowed_emails ORDER BY created_at
    `);

    console.log(`Found ${allowedEmails.length} allowed emails:`);
    allowedEmails.forEach((email, index) => {
      console.log(`${index + 1}. ${email.email}`);
    });

    // 6. Create Default Configuration Script
    console.log('\n📄 Creating Default Configuration Script...');

    const defaultConfigScript = `#!/usr/bin/env node

/**
 * HASSAN'S DIGITAL INVOICE SYSTEM - DEFAULT CONFIGURATION
 * Generated on: ${new Date().toISOString()}
 * 
 * This script contains all your custom configurations:
 * - Email Templates: TVZYON, Directo, AVIX, Smartonn
 * - Custom Checkout Page: Smartonn TEST
 * - Payment Gateway URLs: Storazo, Billgang
 * - SMTP Providers: 5 configured providers
 * - Allowed Emails: Your approved email addresses
 */

const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

// Load environment variables
if (process.env.NODE_ENV === 'production') {
  dotenv.config({ path: '.env.production', override: true });
} else {
  dotenv.config();
}

const DATABASE_URL = process.env.DATABASE_URL || 'mysql://user:XwB8rMNKB3OZzBzyYZft@localhost:3306/db';

// Parse MySQL connection string
const url = new URL(DATABASE_URL);
const connectionConfig = {
  host: url.hostname,
  port: url.port || 3306,
  user: url.username,
  password: url.password,
  database: url.pathname.slice(1),
};

// Your Default Email Templates
const defaultEmailTemplates = ${JSON.stringify(emailTemplates, null, 2)};

// Your Default Custom Checkout Pages
const defaultCheckoutPages = ${JSON.stringify(checkoutPages, null, 2)};

// Your Default Payment Gateway URLs
const defaultPaymentLinks = ${JSON.stringify(paymentLinks, null, 2)};

// Your Default SMTP Providers
const defaultSmtpProviders = ${JSON.stringify(smtpProviders, null, 2)};

// Your Default Allowed Emails
const defaultAllowedEmails = ${JSON.stringify(allowedEmails, null, 2)};

async function setupDefaults() {
  let connection;
  try {
    connection = await mysql.createConnection(connectionConfig);
    console.log('✅ Connected to MySQL database');
    console.log('🚀 Setting up Hassan\\'s default configurations...');

    // Setup Email Templates
    console.log('\\n📝 Setting up email templates...');
    for (const template of defaultEmailTemplates) {
      try {
        await connection.execute(\`
          INSERT INTO email_templates (template_id, name, description, subject, html_content, text_content, category, is_default, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          name = VALUES(name),
          description = VALUES(description),
          subject = VALUES(subject),
          html_content = VALUES(html_content),
          text_content = VALUES(text_content),
          category = VALUES(category),
          is_default = VALUES(is_default),
          updated_at = VALUES(updated_at)
        \`, [
          template.template_id, template.name, template.description, template.subject,
          template.html_content, template.text_content, template.category, template.is_default,
          template.created_at, template.updated_at
        ]);
        console.log(\`✅ Email Template: \${template.name}\`);
      } catch (error) {
        console.log(\`⚠️ Email Template \${template.name} error:\`, error.message);
      }
    }

    // Setup Custom Checkout Pages
    console.log('\\n🛒 Setting up custom checkout pages...');
    for (const page of defaultCheckoutPages) {
      try {
        await connection.execute(\`
          INSERT INTO custom_checkout_pages (
            title, slug, product_name, product_description, price, image_url, payment_method,
            custom_payment_link_id, paypal_button_id, embed_code_id, require_allowed_email, 
            is_trial_checkout, confirmation_message, header_title, footer_text, header_logo, 
            footer_logo, theme_mode, use_referrer_masking, redirect_delay, expires_at,
            active, views, conversions, created_at, updated_at
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          title = VALUES(title),
          product_name = VALUES(product_name),
          product_description = VALUES(product_description),
          price = VALUES(price),
          updated_at = VALUES(updated_at)
        \`, [
          page.title, page.slug, page.product_name, page.product_description, page.price, 
          page.image_url, page.payment_method, page.custom_payment_link_id, page.paypal_button_id,
          page.embed_code_id, page.require_allowed_email, page.is_trial_checkout,
          page.confirmation_message, page.header_title, page.footer_text, page.header_logo, 
          page.footer_logo, page.theme_mode, page.use_referrer_masking, page.redirect_delay, 
          page.expires_at, page.active, page.views, page.conversions, page.created_at, page.updated_at
        ]);
        console.log(\`✅ Checkout Page: \${page.title} (\${page.slug})\`);
      } catch (error) {
        console.log(\`⚠️ Checkout Page \${page.title} error:\`, error.message);
      }
    }

    // Setup Payment Links
    console.log('\\n💳 Setting up payment gateway URLs...');
    
    // Create table if it doesn't exist
    await connection.execute(\`
      CREATE TABLE IF NOT EXISTS custom_payment_links (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        url TEXT NOT NULL,
        button_text VARCHAR(255) NOT NULL DEFAULT 'Complete Payment',
        success_redirect_url TEXT,
        active BOOLEAN NOT NULL DEFAULT TRUE,
        is_trial BOOLEAN NOT NULL DEFAULT FALSE,
        created_at VARCHAR(255) NOT NULL,
        updated_at VARCHAR(255) NOT NULL
      )
    \`);

    for (const link of defaultPaymentLinks) {
      try {
        await connection.execute(\`
          INSERT INTO custom_payment_links (id, name, url, button_text, success_redirect_url, active, is_trial, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          name = VALUES(name),
          url = VALUES(url),
          updated_at = VALUES(updated_at)
        \`, [
          link.id, link.name, link.url, link.button_text, link.success_redirect_url,
          link.active, link.is_trial, link.created_at, link.updated_at
        ]);
        console.log(\`✅ Payment Link: \${link.name} (\${link.url})\`);
      } catch (error) {
        console.log(\`⚠️ Payment Link \${link.name} error:\`, error.message);
      }
    }

    // Setup SMTP Providers
    console.log('\\n📧 Setting up SMTP providers...');
    for (const provider of defaultSmtpProviders) {
      try {
        await connection.execute(\`
          INSERT INTO smtp_providers (id, name, host, port, secure, username, password, from_email, from_name, active, is_default, is_backup, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          name = VALUES(name),
          host = VALUES(host),
          active = VALUES(active),
          is_default = VALUES(is_default),
          is_backup = VALUES(is_backup),
          updated_at = VALUES(updated_at)
        \`, [
          provider.id, provider.name, provider.host, provider.port, provider.secure,
          provider.username, provider.password, provider.from_email, provider.from_name,
          provider.active, provider.is_default, provider.is_backup, provider.created_at, provider.updated_at
        ]);
        console.log(\`✅ SMTP Provider: \${provider.name}\`);
      } catch (error) {
        console.log(\`⚠️ SMTP Provider \${provider.name} error:\`, error.message);
      }
    }

    // Setup Allowed Emails
    console.log('\\n📧 Setting up allowed emails...');
    for (const email of defaultAllowedEmails) {
      try {
        await connection.execute(\`
          INSERT INTO allowed_emails (email, notes, smtp_provider, last_updated, created_at)
          VALUES (?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          notes = VALUES(notes),
          smtp_provider = VALUES(smtp_provider),
          last_updated = VALUES(last_updated)
        \`, [
          email.email, email.notes, email.smtp_provider, email.last_updated, email.created_at
        ]);
        console.log(\`✅ Allowed Email: \${email.email}\`);
      } catch (error) {
        console.log(\`⚠️ Allowed Email \${email.email} error:\`, error.message);
      }
    }

    console.log('\\n🎉 HASSAN\\'S DEFAULT CONFIGURATION SETUP COMPLETED!');
    console.log('\\n📋 SUMMARY:');
    console.log(\`✅ \${defaultEmailTemplates.length} Email Templates (TVZYON, Directo, AVIX, Smartonn)\`);
    console.log(\`✅ \${defaultCheckoutPages.length} Custom Checkout Page (Smartonn TEST)\`);
    console.log(\`✅ \${defaultPaymentLinks.length} Payment Gateway URLs (Storazo, Billgang)\`);
    console.log(\`✅ \${defaultSmtpProviders.length} SMTP Providers\`);
    console.log(\`✅ \${defaultAllowedEmails.length} Allowed Emails\`);
    console.log('\\n🌐 Access your application at: http://localhost:3001');
    console.log('🔧 Admin panel: http://localhost:3001/admin');
    console.log('🛒 Smartonn TEST checkout: http://localhost:3001/checkout/smartonn-test');

  } catch (error) {
    console.error('❌ Error setting up defaults:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
    process.exit(0);
  }
}

// Run the setup
if (require.main === module) {
  setupDefaults();
}

module.exports = { setupDefaults, defaultEmailTemplates, defaultCheckoutPages, defaultPaymentLinks, defaultSmtpProviders, defaultAllowedEmails };
`;

    // Save the default configuration script
    await fs.writeFile('hassan-defaults.cjs', defaultConfigScript);
    console.log('✅ Default configuration script saved as: hassan-defaults.cjs');

    // Create a backup of current data
    console.log('\n💾 Creating backup of current data...');
    const backupData = {
      timestamp: new Date().toISOString(),
      database: DATABASE_URL.replace(/\/\/.*@/, '//***:***@'), // Hide credentials
      emailTemplates,
      checkoutPages,
      paymentLinks,
      smtpProviders,
      allowedEmails
    };

    const backupDir = 'backups';
    try {
      await fs.mkdir(backupDir, { recursive: true });
    } catch (error) {
      // Directory already exists
    }

    const backupFile = path.join(backupDir, `hassan-config-backup-${new Date().toISOString().replace(/[:.]/g, '-')}.json`);
    await fs.writeFile(backupFile, JSON.stringify(backupData, null, 2));
    console.log(`✅ Backup saved as: ${backupFile}`);

    console.log('\n🎉 ALL CONFIGURATIONS SAVED AS DEFAULTS!');
    console.log('\n📋 WHAT WAS SAVED:');
    console.log(`✅ ${emailTemplates.length} Email Templates: TVZYON, Directo, AVIX, Smartonn`);
    console.log(`✅ ${checkoutPages.length} Custom Checkout Page: Smartonn TEST`);
    console.log(`✅ ${paymentLinks.length} Payment Gateway URLs: Storazo, Billgang`);
    console.log(`✅ ${smtpProviders.length} SMTP Providers: AVIXIPTV, DirectoIPTV, Enzidswan, Smartonn, TVZYON`);
    console.log(`✅ ${allowedEmails.length} Allowed Emails: <EMAIL>, <EMAIL>`);

    console.log('\n📁 FILES CREATED:');
    console.log('✅ hassan-defaults.cjs - Your default configuration script');
    console.log(`✅ ${backupFile} - Complete backup of your data`);

    console.log('\n🚀 USAGE:');
    console.log('To restore these defaults on any server, run:');
    console.log('node hassan-defaults.cjs');

  } catch (error) {
    console.error('❌ Error saving defaults:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
    process.exit(0);
  }
}

// Run the save process
saveAsDefaults();
