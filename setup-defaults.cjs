#!/usr/bin/env node

/**
 * Default Configuration Setup Script
 * This script sets up all default configurations for the Digital Invoice system
 * using direct MySQL queries to avoid import issues
 */

const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

// Load environment variables
if (process.env.NODE_ENV === 'production') {
  dotenv.config({ path: '.env.production', override: true });
} else {
  dotenv.config();
}

const DATABASE_URL = process.env.DATABASE_URL || 'mysql://user:XwB8rMNKB3OZzBzyYZft@localhost:3306/db';

console.log('🚀 Setting up default configurations...');

// Parse MySQL connection string
const url = new URL(DATABASE_URL);
const connectionConfig = {
  host: url.hostname,
  port: url.port || 3306,
  user: url.username,
  password: url.password,
  database: url.pathname.slice(1), // Remove leading slash
};

// Default SMTP Providers
const defaultSmtpProviders = [
  {
    id: 'avixiptv-smtp',
    name: 'AVIXIPTV',
    host: 'mail.avixiptv.com',
    port: '587',
    secure: false,
    username: '<EMAIL>',
    password: 'Avix@2024',
    fromEmail: '<EMAIL>',
    fromName: 'AVIXIPTV Support',
    active: true,
    isDefault: true,
    isBackup: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'directoiptv-smtp',
    name: 'DirectoIPTV',
    host: 'mail.directoiptv.com',
    port: '587',
    secure: false,
    username: '<EMAIL>',
    password: 'Directo@2024',
    fromEmail: '<EMAIL>',
    fromName: 'DirectoIPTV Support',
    active: true,
    isDefault: false,
    isBackup: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'enzidswan-smtp',
    name: 'Enzidswan',
    host: 'mail.enzidswan.com',
    port: '587',
    secure: false,
    username: '<EMAIL>',
    password: 'Enzid@2024',
    fromEmail: '<EMAIL>',
    fromName: 'Enzidswan Support',
    active: true,
    isDefault: false,
    isBackup: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'smartonn-smtp',
    name: 'Smartonn',
    host: 'mail.smartonn.com',
    port: '587',
    secure: false,
    username: '<EMAIL>',
    password: 'Smart@2024',
    fromEmail: '<EMAIL>',
    fromName: 'Smartonn Support',
    active: true,
    isDefault: false,
    isBackup: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'tvzyon-smtp',
    name: 'TVZYON',
    host: 'mail.tvzyon.com',
    port: '587',
    secure: false,
    username: '<EMAIL>',
    password: 'Tvzyon@2024',
    fromEmail: '<EMAIL>',
    fromName: 'TVZYON Support',
    active: true,
    isDefault: false,
    isBackup: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
];

// Default Email Templates
const defaultEmailTemplates = [
  {
    templateId: 'iptv-subscription-details',
    name: 'IPTV Subscription Details',
    description: 'Template for sending IPTV subscription details to customers',
    subject: 'Your IPTV Subscription Details',
    htmlContent: '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;"><h2 style="color: #333;">Your IPTV Subscription Details</h2><p>Dear {{customerName}},</p><p>Thank you for your purchase! Here are your IPTV subscription details:</p><div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;"><h3 style="margin-top: 0;">Subscription Information:</h3><p><strong>M3U Link:</strong><br>http://pythontv.net:2052/get.php?username={{username}}&password={{password}}&type=m3u_plus&output=ts</p><p><strong>Username:</strong> {{username}}<br><strong>Password:</strong> {{password}}</p><p><strong>Server Links:</strong><br>• http://premium.pro4ott.com:8789<br>• http://live.mypythontv.com:2052<br>• http://mypythonpremium.com:2052</p></div><p><strong>📱 Installation Guide:</strong><br><a href="https://directoiptv.com/install/">https://directoiptv.com/install/</a></p><p><strong>📧 Support:</strong><br>If you have any problems, contact us at:<br>Email: <EMAIL></p><p>Thank you for choosing our IPTV service!<br>DirectoIPTV Team</p></div>',
    textContent: 'Your IPTV Subscription Details\n\nDear {{customerName}},\n\nThank you for your purchase! Here are your IPTV subscription details:\n\nM3U Link: http://pythontv.net:2052/get.php?username={{username}}&password={{password}}&type=m3u_plus&output=ts\n\nUsername: {{username}}\nPassword: {{password}}\n\nServer Links:\n- http://premium.pro4ott.com:8789\n- http://live.mypythontv.com:2052\n- http://mypythonpremium.com:2052\n\nInstallation Guide: https://directoiptv.com/install/\n\nSupport: <EMAIL>\n\nThank you for choosing our IPTV service!\nDirectoIPTV Team',
    category: 'subscription',
    isDefault: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    templateId: 'payment-required',
    name: 'Payment Required',
    description: 'Template for payment required notifications',
    subject: 'Payment Required for {{productName}}',
    htmlContent: '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;"><h2 style="color: #333;">Payment Required</h2><p>Dear {{customerName}},</p><p>Thank you for your order! Your payment for {{productName}} is ready to be processed.</p><div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;"><h3 style="margin-top: 0;">Order Details:</h3><p><strong>Product:</strong> {{productName}}<br><strong>Amount:</strong> ${{amount}}<br><strong>Order Date:</strong> {{orderDate}}</p></div><div style="text-align: center; margin: 30px 0;"><a href="{{paymentUrl}}" style="background-color: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">Complete Payment</a></div><p><strong>Important:</strong> After payment confirmation, you will receive your subscription details within 8 hours during working hours. Most subscriptions are delivered within 3 hours.</p><p>If you have any questions, please reply to this email.</p><p>Thank you,<br>Support Team</p></div>',
    textContent: 'Payment Required\n\nDear {{customerName}},\n\nThank you for your order! Your payment for {{productName}} is ready to be processed.\n\nOrder Details:\nProduct: {{productName}}\nAmount: ${{amount}}\nOrder Date: {{orderDate}}\n\nTo complete your payment, please visit: {{paymentUrl}}\n\nImportant: After payment confirmation, you will receive your subscription details within 8 hours during working hours. Most subscriptions are delivered within 3 hours.\n\nIf you have any questions, please reply to this email.\n\nThank you,\nSupport Team',
    category: 'payment',
    isDefault: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    templateId: 'trial-subscription',
    name: 'Trial Subscription',
    description: 'Template for trial subscription notifications',
    subject: 'Your Trial Subscription is Ready',
    htmlContent: '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;"><h2 style="color: #333;">Your Trial Subscription is Ready!</h2><p>Dear {{customerName}},</p><p>Welcome to your trial subscription! Here are your temporary access details:</p><div style="background-color: #e6f7ff; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #1890ff;"><h3 style="margin-top: 0; color: #1890ff;">Trial Access Details:</h3><p><strong>Trial Duration:</strong> 24 hours<br><strong>Username:</strong> {{trialUsername}}<br><strong>Password:</strong> {{trialPassword}}</p><p><strong>M3U Link:</strong><br>http://pythontv.net:2052/get.php?username={{trialUsername}}&password={{trialPassword}}&type=m3u_plus&output=ts</p></div><div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;"><p style="margin: 0;"><strong>⚠️ Important:</strong> This is a trial subscription valid for 24 hours only. To continue enjoying our service, please upgrade to a full subscription.</p></div><div style="text-align: center; margin: 30px 0;"><a href="{{upgradeUrl}}" style="background-color: #52c41a; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">Upgrade to Full Subscription</a></div><p><strong>📱 Installation Guide:</strong><br><a href="https://directoiptv.com/install/">https://directoiptv.com/install/</a></p><p>Enjoy your trial!<br>DirectoIPTV Team</p></div>',
    textContent: 'Your Trial Subscription is Ready!\n\nDear {{customerName}},\n\nWelcome to your trial subscription! Here are your temporary access details:\n\nTrial Duration: 24 hours\nUsername: {{trialUsername}}\nPassword: {{trialPassword}}\n\nM3U Link: http://pythontv.net:2052/get.php?username={{trialUsername}}&password={{trialPassword}}&type=m3u_plus&output=ts\n\nImportant: This is a trial subscription valid for 24 hours only. To continue enjoying our service, please upgrade to a full subscription.\n\nUpgrade URL: {{upgradeUrl}}\n\nInstallation Guide: https://directoiptv.com/install/\n\nEnjoy your trial!\nDirectoIPTV Team',
    category: 'trial',
    isDefault: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
];

// Default Allowed Emails
const defaultAllowedEmails = [
  {
    email: '<EMAIL>',
    notes: 'Default admin email - Hassan Khalid',
    smtpProvider: 'avixiptv-smtp',
    lastUpdated: new Date().toISOString(),
    createdAt: new Date().toISOString(),
  }
];

// Default Custom Checkout Page
const defaultCheckoutPage = {
  title: 'Premium IPTV Subscription',
  slug: 'premium-iptv',
  productName: 'IPTV 12 Months Premium',
  productDescription: 'Premium IPTV service with 12 months access to thousands of channels worldwide. Includes sports, movies, TV shows, and more.',
  price: 48.00,
  imageUrl: '/uploads/IPTV-12M.jpg',
  paymentMethod: 'custom-link',
  customPaymentLinkId: 'link-1',
  paypalButtonId: '',
  trialCustomPaymentLinkId: '',
  trialPaypalButtonId: '',
  embedCodeId: '',
  requireUsername: true,
  requireAllowedEmail: true,
  isTrialCheckout: false,
  confirmationMessage: '<div class="space-y-3"><p><strong>🛒 Ready to complete your purchase?</strong></p><p>✅ <strong>What you\'re getting:</strong></p><ul class="list-disc list-inside space-y-1"><li>12 months of premium IPTV access</li><li>Thousands of channels worldwide</li><li>HD/4K quality streaming</li><li>24/7 customer support</li><li>Multiple device compatibility</li><li>Instant activation</li></ul><p class="text-sm text-muted-foreground mt-3">💳 <em>Your payment will be processed securely</em></p><p class="text-xs text-muted-foreground">By proceeding, you agree to our terms of service and privacy policy.</p></div>',
  headerTitle: 'Premium IPTV Service',
  footerText: 'Secure payment processing • 24/7 support • Instant delivery',
  headerLogo: '',
  footerLogo: '',
  themeMode: 'light',
  useReferrerMasking: false,
  redirectDelay: 2000,
  smtpProviderId: 'avixiptv-smtp',
  expiresAt: null,
  active: true,
  views: 0,
  conversions: 0,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

async function setupDefaults() {
  let connection;
  try {
    connection = await mysql.createConnection(connectionConfig);
    console.log('✅ Connected to MySQL database');

    console.log('📧 Setting up default SMTP providers...');
    for (const provider of defaultSmtpProviders) {
      try {
        await connection.execute(`
          INSERT INTO smtp_providers (id, name, host, port, secure, username, password, from_email, from_name, active, is_default, is_backup, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          name = VALUES(name),
          host = VALUES(host),
          port = VALUES(port),
          secure = VALUES(secure),
          username = VALUES(username),
          password = VALUES(password),
          from_email = VALUES(from_email),
          from_name = VALUES(from_name),
          active = VALUES(active),
          is_default = VALUES(is_default),
          is_backup = VALUES(is_backup),
          updated_at = VALUES(updated_at)
        `, [
          provider.id, provider.name, provider.host, provider.port, provider.secure,
          provider.username, provider.password, provider.fromEmail, provider.fromName,
          provider.active, provider.isDefault, provider.isBackup, provider.createdAt, provider.updatedAt
        ]);
        console.log(`✅ SMTP Provider: ${provider.name}`);
      } catch (error) {
        console.log(`⚠️ SMTP Provider ${provider.name} error:`, error.message);
      }
    }

    console.log('📝 Setting up default email templates...');
    for (const template of defaultEmailTemplates) {
      try {
        await connection.execute(`
          INSERT INTO email_templates (template_id, name, description, subject, html_content, text_content, category, is_default, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          name = VALUES(name),
          description = VALUES(description),
          subject = VALUES(subject),
          html_content = VALUES(html_content),
          text_content = VALUES(text_content),
          category = VALUES(category),
          is_default = VALUES(is_default),
          updated_at = VALUES(updated_at)
        `, [
          template.templateId, template.name, template.description, template.subject,
          template.htmlContent, template.textContent, template.category, template.isDefault,
          template.createdAt, template.updatedAt
        ]);
        console.log(`✅ Email Template: ${template.name}`);
      } catch (error) {
        console.log(`⚠️ Email Template ${template.name} error:`, error.message);
      }
    }

    console.log('📧 Setting up default allowed emails...');
    for (const email of defaultAllowedEmails) {
      try {
        await connection.execute(`
          INSERT INTO allowed_emails (email, notes, smtp_provider, last_updated, created_at)
          VALUES (?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          notes = VALUES(notes),
          smtp_provider = VALUES(smtp_provider),
          last_updated = VALUES(last_updated)
        `, [
          email.email, email.notes, email.smtpProvider, email.lastUpdated, email.createdAt
        ]);
        console.log(`✅ Allowed Email: ${email.email}`);
      } catch (error) {
        console.log(`⚠️ Allowed Email ${email.email} error:`, error.message);
      }
    }

    console.log('🛒 Setting up default checkout page...');
    try {
      await connection.execute(`
        INSERT INTO custom_checkout_pages (
          title, slug, product_name, product_description, price, image_url, payment_method,
          custom_payment_link_id, paypal_button_id, trial_custom_payment_link_id, trial_paypal_button_id,
          embed_code_id, require_username, require_allowed_email, is_trial_checkout,
          confirmation_message, header_title, footer_text, header_logo, footer_logo,
          theme_mode, use_referrer_masking, redirect_delay, smtp_provider_id, expires_at,
          active, views, conversions, created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        title = VALUES(title),
        product_name = VALUES(product_name),
        product_description = VALUES(product_description),
        price = VALUES(price),
        image_url = VALUES(image_url),
        payment_method = VALUES(payment_method),
        custom_payment_link_id = VALUES(custom_payment_link_id),
        confirmation_message = VALUES(confirmation_message),
        header_title = VALUES(header_title),
        footer_text = VALUES(footer_text),
        smtp_provider_id = VALUES(smtp_provider_id),
        updated_at = VALUES(updated_at)
      `, [
        defaultCheckoutPage.title, defaultCheckoutPage.slug, defaultCheckoutPage.productName,
        defaultCheckoutPage.productDescription, defaultCheckoutPage.price, defaultCheckoutPage.imageUrl,
        defaultCheckoutPage.paymentMethod, defaultCheckoutPage.customPaymentLinkId, defaultCheckoutPage.paypalButtonId,
        defaultCheckoutPage.trialCustomPaymentLinkId, defaultCheckoutPage.trialPaypalButtonId, defaultCheckoutPage.embedCodeId,
        defaultCheckoutPage.requireUsername, defaultCheckoutPage.requireAllowedEmail, defaultCheckoutPage.isTrialCheckout,
        defaultCheckoutPage.confirmationMessage, defaultCheckoutPage.headerTitle, defaultCheckoutPage.footerText,
        defaultCheckoutPage.headerLogo, defaultCheckoutPage.footerLogo, defaultCheckoutPage.themeMode,
        defaultCheckoutPage.useReferrerMasking, defaultCheckoutPage.redirectDelay, defaultCheckoutPage.smtpProviderId,
        defaultCheckoutPage.expiresAt, defaultCheckoutPage.active, defaultCheckoutPage.views,
        defaultCheckoutPage.conversions, defaultCheckoutPage.createdAt, defaultCheckoutPage.updatedAt
      ]);
      console.log(`✅ Checkout Page: ${defaultCheckoutPage.title}`);
    } catch (error) {
      console.log(`⚠️ Checkout Page error:`, error.message);
    }

    console.log('\n🎉 Default configuration setup completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`✅ ${defaultSmtpProviders.length} SMTP Providers configured`);
    console.log(`✅ ${defaultEmailTemplates.length} Email Templates created`);
    console.log(`✅ ${defaultAllowedEmails.length} Allowed Emails added`);
    console.log(`✅ 1 Custom Checkout Page created`);
    console.log('\n🌐 Access your application at: http://localhost:3001');
    console.log('🔧 Admin panel: http://localhost:3001/admin');
    console.log('🛒 Checkout page: http://localhost:3001/checkout/premium-iptv');

  } catch (error) {
    console.error('❌ Error setting up defaults:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
    process.exit(0);
  }
}

// Run the setup
setupDefaults();
