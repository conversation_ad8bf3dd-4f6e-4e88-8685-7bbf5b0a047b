const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

// Load environment variables
if (process.env.NODE_ENV === 'production') {
  dotenv.config({ path: '.env.production', override: true });
} else {
  dotenv.config();
}

const DATABASE_URL = process.env.DATABASE_URL || 'mysql://user:XwB8rMNKB3OZzBzyYZft@localhost:3306/db';

// Parse MySQL connection string
const url = new URL(DATABASE_URL);
const connectionConfig = {
  host: url.hostname,
  port: url.port || 3306,
  user: url.username,
  password: url.password,
  database: url.pathname.slice(1), // Remove leading slash
};

async function checkCurrentConfig() {
  let connection;
  try {
    connection = await mysql.createConnection(connectionConfig);
    console.log('✅ Connected to MySQL database');

    console.log('\n📝 CURRENT EMAIL TEMPLATES:');
    console.log('=' .repeat(50));
    const [emailTemplates] = await connection.execute(`
      SELECT template_id, name, description, subject, category, is_default,
             html_content, text_content, created_at, updated_at
      FROM email_templates
      ORDER BY created_at DESC
    `);

    if (emailTemplates.length === 0) {
      console.log('❌ No email templates found');
    } else {
      emailTemplates.forEach((template, index) => {
        console.log(`${index + 1}. Template ID: ${template.template_id}`);
        console.log(`   Name: ${template.name}`);
        console.log(`   Description: ${template.description || 'No description'}`);
        console.log(`   Subject: ${template.subject}`);
        console.log(`   Category: ${template.category}`);
        console.log(`   Default: ${template.is_default ? 'Yes' : 'No'}`);
        console.log(`   HTML Content Length: ${template.html_content ? template.html_content.length : 0} chars`);
        console.log(`   Text Content Length: ${template.text_content ? template.text_content.length : 0} chars`);
        console.log(`   Created: ${template.created_at}`);
        console.log(`   Updated: ${template.updated_at}`);
        console.log('');
      });
    }

    console.log('\n🛒 CURRENT CUSTOM CHECKOUT PAGES:');
    console.log('=' .repeat(50));

    // First check what columns exist
    const [columns] = await connection.execute(`
      SHOW COLUMNS FROM custom_checkout_pages
    `);
    console.log('Available columns in custom_checkout_pages:');
    columns.forEach(col => console.log(`   - ${col.Field}`));
    console.log('');

    const [checkoutPages] = await connection.execute(`
      SELECT * FROM custom_checkout_pages
      ORDER BY created_at DESC
    `);

    if (checkoutPages.length === 0) {
      console.log('❌ No custom checkout pages found');
    } else {
      checkoutPages.forEach((page, index) => {
        console.log(`${index + 1}. Page ID: ${page.id}`);
        console.log(`   Title: ${page.title || 'No title'}`);
        console.log(`   Slug: ${page.slug || 'No slug'}`);
        console.log(`   Product: ${page.product_name || 'No product name'}`);
        console.log(`   Description: ${page.product_description ? page.product_description.substring(0, 100) + '...' : 'No description'}`);
        console.log(`   Price: $${page.price || '0'}`);
        console.log(`   Payment Method: ${page.payment_method || 'None'}`);
        console.log(`   Custom Payment Link ID: ${page.custom_payment_link_id || 'None'}`);
        console.log(`   PayPal Button ID: ${page.paypal_button_id || 'None'}`);
        console.log(`   Header Title: ${page.header_title || 'None'}`);
        console.log(`   Footer Text: ${page.footer_text ? page.footer_text.substring(0, 50) + '...' : 'None'}`);
        console.log(`   Active: ${page.active ? 'Yes' : 'No'}`);
        console.log(`   Views: ${page.views || 0}`);
        console.log(`   Conversions: ${page.conversions || 0}`);
        console.log(`   URL: http://localhost:3001/checkout/${page.slug}`);
        console.log(`   Created: ${page.created_at}`);
        console.log(`   Updated: ${page.updated_at || 'Not updated'}`);

        // Show confirmation message if it contains payment URLs
        if (page.confirmation_message && (page.confirmation_message.includes('shippz.bgng.io') || page.confirmation_message.includes('storazo.com'))) {
          console.log(`   ⭐ CONTAINS PAYMENT URLs in confirmation message!`);
        }
        console.log('');
      });
    }

    // Check for payment gateway URLs in various tables
    console.log('\n💳 SEARCHING FOR PAYMENT GATEWAY URLs:');
    console.log('=' .repeat(50));

    // Check custom payment links table if it exists
    try {
      const [paymentLinks] = await connection.execute(`
        SELECT * FROM custom_payment_links ORDER BY created_at DESC
      `);
      if (paymentLinks.length > 0) {
        console.log('📋 Custom Payment Links:');
        paymentLinks.forEach((link, index) => {
          console.log(`${index + 1}. ID: ${link.id}`);
          console.log(`   Name: ${link.name || 'Unnamed'}`);
          console.log(`   URL: ${link.url || link.link_url || 'No URL'}`);
          console.log(`   Created: ${link.created_at}`);
          console.log('');
        });
      }
    } catch (error) {
      console.log('⚠️ Custom payment links table not found or empty');
    }

    // Check system messages for URLs
    try {
      const [systemMessages] = await connection.execute(`
        SELECT message_id, name, content FROM system_messages
        WHERE content LIKE '%shippz.bgng.io%' OR content LIKE '%storazo.com%'
        ORDER BY created_at DESC
      `);
      if (systemMessages.length > 0) {
        console.log('📋 System Messages with Payment URLs:');
        systemMessages.forEach((msg, index) => {
          console.log(`${index + 1}. Message ID: ${msg.message_id}`);
          console.log(`   Name: ${msg.name}`);
          console.log(`   Content: ${msg.content.substring(0, 200)}...`);
          console.log('');
        });
      }
    } catch (error) {
      console.log('⚠️ No system messages with payment URLs found');
    }

    // Search all text fields for the URLs
    console.log('\n🔍 SEARCHING ALL TABLES FOR PAYMENT URLs:');
    console.log('=' .repeat(50));

    const searchUrls = ['shippz.bgng.io', 'storazo.com'];
    for (const searchUrl of searchUrls) {
      console.log(`\n🔍 Searching for: ${searchUrl}`);

      // Search in checkout pages
      const [checkoutResults] = await connection.execute(`
        SELECT id, title, slug, confirmation_message FROM custom_checkout_pages
        WHERE confirmation_message LIKE ? OR product_description LIKE ? OR footer_text LIKE ?
      `, [`%${searchUrl}%`, `%${searchUrl}%`, `%${searchUrl}%`]);

      if (checkoutResults.length > 0) {
        console.log(`   Found in checkout pages: ${checkoutResults.length} results`);
        checkoutResults.forEach(result => {
          console.log(`   - Page: ${result.title} (${result.slug})`);
        });
      }

      // Search in email templates
      const [emailResults] = await connection.execute(`
        SELECT template_id, name, html_content, text_content FROM email_templates
        WHERE html_content LIKE ? OR text_content LIKE ? OR subject LIKE ?
      `, [`%${searchUrl}%`, `%${searchUrl}%`, `%${searchUrl}%`]);

      if (emailResults.length > 0) {
        console.log(`   Found in email templates: ${emailResults.length} results`);
        emailResults.forEach(result => {
          console.log(`   - Template: ${result.name} (${result.template_id})`);
        });
      }
    }

    console.log('\n📧 CURRENT SMTP PROVIDERS:');
    console.log('=' .repeat(50));
    const [smtpProviders] = await connection.execute(`
      SELECT id, name, from_email, active, is_default, is_backup, created_at
      FROM smtp_providers 
      ORDER BY is_default DESC, is_backup ASC, created_at DESC
    `);
    
    if (smtpProviders.length === 0) {
      console.log('❌ No SMTP providers found');
    } else {
      smtpProviders.forEach((provider, index) => {
        console.log(`${index + 1}. Provider ID: ${provider.id}`);
        console.log(`   Name: ${provider.name}`);
        console.log(`   From Email: ${provider.from_email}`);
        console.log(`   Active: ${provider.active ? 'Yes' : 'No'}`);
        console.log(`   Default: ${provider.is_default ? 'Yes' : 'No'}`);
        console.log(`   Backup: ${provider.is_backup ? 'Yes' : 'No'}`);
        console.log(`   Created: ${provider.created_at}`);
        console.log('');
      });
    }

    console.log('\n📧 CURRENT ALLOWED EMAILS:');
    console.log('=' .repeat(50));
    const [allowedEmails] = await connection.execute(`
      SELECT email, notes, smtp_provider, created_at
      FROM allowed_emails 
      ORDER BY created_at DESC
    `);
    
    if (allowedEmails.length === 0) {
      console.log('❌ No allowed emails found');
    } else {
      allowedEmails.forEach((email, index) => {
        console.log(`${index + 1}. Email: ${email.email}`);
        console.log(`   Notes: ${email.notes || 'No notes'}`);
        console.log(`   SMTP Provider: ${email.smtp_provider || 'Default'}`);
        console.log(`   Created: ${email.created_at}`);
        console.log('');
      });
    }

  } catch (error) {
    console.error('❌ Error checking current configuration:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
    process.exit(0);
  }
}

// Run the check
checkCurrentConfig();
