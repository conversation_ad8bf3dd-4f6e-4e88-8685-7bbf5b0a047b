Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
🔍 DynamicHomepage Debug: 
Object { isLoadingConfig: true, error: undefined, homepageConfig: null, products: 0, isLoadingProducts: true }
DynamicHomepage.tsx:35:10
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
Uncaught ReferenceError: React is not defined
    Skeleton skeleton.tsx:7
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require chunk-WOOG5QLI.js:12
    js index.js:6
    __require chunk-WOOG5QLI.js:12
    React 2
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    js React
    __require chunk-WOOG5QLI.js:12
    <anonymous> react-dom_client.js:38
skeleton.tsx:7:2
The above error occurred in the <Skeleton> component:

Skeleton@http://localhost:3001/src/components/ui/skeleton.tsx:2:18
div
div
main
div
DynamicHomepage@http://localhost:3001/src/components/homepage/DynamicHomepage.tsx?t=1748902436536:12:25
Home@http://localhost:3001/src/pages/Home.tsx?t=1748902436536:5:49
Route@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/wouter.js?v=b0e64914:323:13
Switch@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/wouter.js?v=b0e64914:379:14
Router@http://localhost:3001/src/App.tsx?t=1748902641273&v=-WfiBeXOuTm46xoSVL2is:55:9
ConfirmationDialogProvider@http://localhost:3001/src/hooks/use-confirmation-dialog.tsx:34:43
ErrorDialogProvider@http://localhost:3001/src/hooks/use-error-dialog.tsx:30:36
SystemMessagesProvider@http://localhost:3001/src/hooks/use-system-messages.tsx:5:39
Provider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/chunk-ZD7ADHDP.js?v=b0e64914:38:47
TooltipProvider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=b0e64914:68:7
QueryClientProvider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/@tanstack_react-query.js?v=b0e64914:2804:27
App

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries. react-dom.development.js:18704:14
The above error occurred in the <Skeleton> component:

Skeleton@http://localhost:3001/src/components/ui/skeleton.tsx:2:18
div
div
main
div
DynamicHomepage@http://localhost:3001/src/components/homepage/DynamicHomepage.tsx?t=1748902436536:12:25
Home@http://localhost:3001/src/pages/Home.tsx?t=1748902436536:5:49
Route@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/wouter.js?v=b0e64914:323:13
Switch@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/wouter.js?v=b0e64914:379:14
Router@http://localhost:3001/src/App.tsx?t=1748902641273&v=-WfiBeXOuTm46xoSVL2is:55:9
ConfirmationDialogProvider@http://localhost:3001/src/hooks/use-confirmation-dialog.tsx:34:43
ErrorDialogProvider@http://localhost:3001/src/hooks/use-error-dialog.tsx:30:36
SystemMessagesProvider@http://localhost:3001/src/hooks/use-system-messages.tsx:5:39
Provider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/chunk-ZD7ADHDP.js?v=b0e64914:38:47
TooltipProvider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=b0e64914:68:7
QueryClientProvider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/@tanstack_react-query.js?v=b0e64914:2804:27
App

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries. react-dom.development.js:18704:14
The above error occurred in the <Skeleton> component:

Skeleton@http://localhost:3001/src/components/ui/skeleton.tsx:2:18
div
div
main
div
DynamicHomepage@http://localhost:3001/src/components/homepage/DynamicHomepage.tsx?t=1748902436536:12:25
Home@http://localhost:3001/src/pages/Home.tsx?t=1748902436536:5:49
Route@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/wouter.js?v=b0e64914:323:13
Switch@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/wouter.js?v=b0e64914:379:14
Router@http://localhost:3001/src/App.tsx?t=1748902641273&v=-WfiBeXOuTm46xoSVL2is:55:9
ConfirmationDialogProvider@http://localhost:3001/src/hooks/use-confirmation-dialog.tsx:34:43
ErrorDialogProvider@http://localhost:3001/src/hooks/use-error-dialog.tsx:30:36
SystemMessagesProvider@http://localhost:3001/src/hooks/use-system-messages.tsx:5:39
Provider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/chunk-ZD7ADHDP.js?v=b0e64914:38:47
TooltipProvider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=b0e64914:68:7
QueryClientProvider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/@tanstack_react-query.js?v=b0e64914:2804:27
App

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries. react-dom.development.js:18704:14
The above error occurred in the <Skeleton> component:

Skeleton@http://localhost:3001/src/components/ui/skeleton.tsx:2:18
div
div
main
div
DynamicHomepage@http://localhost:3001/src/components/homepage/DynamicHomepage.tsx?t=1748902436536:12:25
Home@http://localhost:3001/src/pages/Home.tsx?t=1748902436536:5:49
Route@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/wouter.js?v=b0e64914:323:13
Switch@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/wouter.js?v=b0e64914:379:14
Router@http://localhost:3001/src/App.tsx?t=1748902641273&v=-WfiBeXOuTm46xoSVL2is:55:9
ConfirmationDialogProvider@http://localhost:3001/src/hooks/use-confirmation-dialog.tsx:34:43
ErrorDialogProvider@http://localhost:3001/src/hooks/use-error-dialog.tsx:30:36
SystemMessagesProvider@http://localhost:3001/src/hooks/use-system-messages.tsx:5:39
Provider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/chunk-ZD7ADHDP.js?v=b0e64914:38:47
TooltipProvider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=b0e64914:68:7
QueryClientProvider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/@tanstack_react-query.js?v=b0e64914:2804:27
App

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries. react-dom.development.js:18704:14
The above error occurred in the <Skeleton> component:

Skeleton@http://localhost:3001/src/components/ui/skeleton.tsx:2:18
div
div
main
div
DynamicHomepage@http://localhost:3001/src/components/homepage/DynamicHomepage.tsx?t=1748902436536:12:25
Home@http://localhost:3001/src/pages/Home.tsx?t=1748902436536:5:49
Route@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/wouter.js?v=b0e64914:323:13
Switch@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/wouter.js?v=b0e64914:379:14
Router@http://localhost:3001/src/App.tsx?t=1748902641273&v=-WfiBeXOuTm46xoSVL2is:55:9
ConfirmationDialogProvider@http://localhost:3001/src/hooks/use-confirmation-dialog.tsx:34:43
ErrorDialogProvider@http://localhost:3001/src/hooks/use-error-dialog.tsx:30:36
SystemMessagesProvider@http://localhost:3001/src/hooks/use-system-messages.tsx:5:39
Provider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/chunk-ZD7ADHDP.js?v=b0e64914:38:47
TooltipProvider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=b0e64914:68:7
QueryClientProvider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/@tanstack_react-query.js?v=b0e64914:2804:27
App

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries. react-dom.development.js:18704:14
The above error occurred in the <Skeleton> component:

Skeleton@http://localhost:3001/src/components/ui/skeleton.tsx:2:18
div
div
div
div
main
div
DynamicHomepage@http://localhost:3001/src/components/homepage/DynamicHomepage.tsx?t=1748902436536:12:25
Home@http://localhost:3001/src/pages/Home.tsx?t=1748902436536:5:49
Route@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/wouter.js?v=b0e64914:323:13
Switch@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/wouter.js?v=b0e64914:379:14
Router@http://localhost:3001/src/App.tsx?t=1748902641273&v=-WfiBeXOuTm46xoSVL2is:55:9
ConfirmationDialogProvider@http://localhost:3001/src/hooks/use-confirmation-dialog.tsx:34:43
ErrorDialogProvider@http://localhost:3001/src/hooks/use-error-dialog.tsx:30:36
SystemMessagesProvider@http://localhost:3001/src/hooks/use-system-messages.tsx:5:39
Provider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/chunk-ZD7ADHDP.js?v=b0e64914:38:47
TooltipProvider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=b0e64914:68:7
QueryClientProvider@http://localhost:3001/@fs/C:/Users/<USER>/Desktop/Replit/Digital%20Invoice%2061/node_modules/.vite/deps/@tanstack_react-query.js?v=b0e64914:2804:27
App

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries. react-dom.development.js:18704:14