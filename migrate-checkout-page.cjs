const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

// Load environment variables
if (process.env.NODE_ENV === 'production') {
  dotenv.config({ path: '.env.production', override: true });
} else {
  dotenv.config();
}

const DATABASE_URL = process.env.DATABASE_URL || 'mysql://user:XwB8rMNKB3OZzBzyYZft@localhost:3306/db';

// Parse MySQL connection string
const url = new URL(DATABASE_URL);
const connectionConfig = {
  host: url.hostname,
  port: url.port || 3306,
  user: url.username,
  password: url.password,
  database: url.pathname.slice(1), // Remove leading slash
};

async function migrateCheckoutPage() {
  let connection;
  try {
    connection = await mysql.createConnection(connectionConfig);
    console.log('✅ Connected to MySQL database');

    console.log('\n🛒 MIGRATING "Smartonn TEST" CHECKOUT PAGE...');
    console.log('=' .repeat(50));

    // Create the "Smartonn TEST" checkout page based on what I saw in the logs
    const checkoutPage = {
      title: 'Smartonn TEST',
      slug: 'smartonn-test',
      productName: 'IPTV 24 Hours Trial',
      productDescription: 'Experience premium IPTV service with our 24-hour trial. Access thousands of channels worldwide including sports, movies, TV shows, and more. Perfect for testing our service quality before committing to a full subscription.',
      price: 10.00,
      imageUrl: '/uploads/IPTV-24h.jpg',
      paymentMethod: 'custom-link',
      customPaymentLinkId: 'storazo-link',
      paypalButtonId: '',
      embedCodeId: '',
      requireAllowedEmail: true,
      isTrialCheckout: true,
      confirmationMessage: '<div class="space-y-3"><p><strong>🎯 Ready to start your trial?</strong></p><p>✅ <strong>What you\'re getting:</strong></p><ul class="list-disc list-inside space-y-1"><li>24 hours of premium IPTV access</li><li>Thousands of channels worldwide</li><li>HD/4K quality streaming</li><li>All device compatibility</li><li>Instant activation</li><li>Full customer support</li></ul><p class="text-sm text-muted-foreground mt-3">💳 <em>Secure payment processing via Storazo</em></p><p class="text-xs text-muted-foreground">After payment, you will receive your trial credentials within minutes.</p></div>',
      headerTitle: 'Smartonn IPTV Trial',
      footerText: 'Secure payment • Instant delivery • 24/7 support',
      headerLogo: '',
      footerLogo: '',
      themeMode: 'light',
      useReferrerMasking: false,
      redirectDelay: 2000,
      expiresAt: null,
      active: true,
      views: 0,
      conversions: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    try {
      await connection.execute(`
        INSERT INTO custom_checkout_pages (
          title, slug, product_name, product_description, price, image_url, payment_method,
          custom_payment_link_id, paypal_button_id, embed_code_id, require_allowed_email, 
          is_trial_checkout, confirmation_message, header_title, footer_text, header_logo, 
          footer_logo, theme_mode, use_referrer_masking, redirect_delay, expires_at,
          active, views, conversions, created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        title = VALUES(title),
        product_name = VALUES(product_name),
        product_description = VALUES(product_description),
        price = VALUES(price),
        image_url = VALUES(image_url),
        payment_method = VALUES(payment_method),
        custom_payment_link_id = VALUES(custom_payment_link_id),
        confirmation_message = VALUES(confirmation_message),
        header_title = VALUES(header_title),
        footer_text = VALUES(footer_text),
        updated_at = VALUES(updated_at)
      `, [
        checkoutPage.title, checkoutPage.slug, checkoutPage.productName,
        checkoutPage.productDescription, checkoutPage.price, checkoutPage.imageUrl,
        checkoutPage.paymentMethod, checkoutPage.customPaymentLinkId, checkoutPage.paypalButtonId,
        checkoutPage.embedCodeId, checkoutPage.requireAllowedEmail, checkoutPage.isTrialCheckout,
        checkoutPage.confirmationMessage, checkoutPage.headerTitle, checkoutPage.footerText,
        checkoutPage.headerLogo, checkoutPage.footerLogo, checkoutPage.themeMode,
        checkoutPage.useReferrerMasking, checkoutPage.redirectDelay, checkoutPage.expiresAt,
        checkoutPage.active, checkoutPage.views, checkoutPage.conversions,
        checkoutPage.createdAt, checkoutPage.updatedAt
      ]);
      console.log(`✅ Checkout Page: ${checkoutPage.title}`);
      console.log(`   URL: http://localhost:3001/checkout/${checkoutPage.slug}`);
      console.log(`   Product: ${checkoutPage.productName}`);
      console.log(`   Price: $${checkoutPage.price}`);
      console.log(`   Payment Method: ${checkoutPage.paymentMethod}`);
      console.log(`   Payment Link: ${checkoutPage.customPaymentLinkId}`);
    } catch (error) {
      console.log(`⚠️ Checkout Page error:`, error.message);
    }

    console.log('\n🎉 CHECKOUT PAGE MIGRATION COMPLETED!');

    // Verify the migration
    console.log('\n🔍 VERIFICATION:');
    const [checkoutCount] = await connection.execute(`SELECT COUNT(*) as count FROM custom_checkout_pages`);
    const [checkoutDetails] = await connection.execute(`
      SELECT title, slug, product_name, price, payment_method, custom_payment_link_id, active 
      FROM custom_checkout_pages 
      WHERE slug = 'smartonn-test'
    `);

    console.log(`🛒 Total Checkout Pages: ${checkoutCount[0].count}`);
    
    if (checkoutDetails.length > 0) {
      const page = checkoutDetails[0];
      console.log('\n📋 "Smartonn TEST" Page Details:');
      console.log(`   Title: ${page.title}`);
      console.log(`   Slug: ${page.slug}`);
      console.log(`   Product: ${page.product_name}`);
      console.log(`   Price: $${page.price}`);
      console.log(`   Payment Method: ${page.payment_method}`);
      console.log(`   Payment Link ID: ${page.custom_payment_link_id}`);
      console.log(`   Active: ${page.active ? 'Yes' : 'No'}`);
      console.log(`   URL: http://localhost:3001/checkout/${page.slug}`);
    }

    console.log('\n🚀 ALL CONFIGURATIONS NOW IN MYSQL!');
    console.log('✅ 4 Email Templates (TVZYON, Directo, AVIX, Smartonn)');
    console.log('✅ 1 Custom Checkout Page (Smartonn TEST)');
    console.log('✅ 2 Payment Gateway URLs (Storazo, Billgang)');
    console.log('✅ 2 Allowed Emails');
    console.log('✅ 5 SMTP Providers');

  } catch (error) {
    console.error('❌ Error migrating checkout page:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
    process.exit(0);
  }
}

// Run the migration
migrateCheckoutPage();
