const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

// Load environment variables
if (process.env.NODE_ENV === 'production') {
  dotenv.config({ path: '.env.production', override: true });
} else {
  dotenv.config();
}

const DATABASE_URL = process.env.DATABASE_URL || 'mysql://user:XwB8rMNKB3OZzBzyYZft@localhost:3306/db';

// Parse MySQL connection string
const url = new URL(DATABASE_URL);
const connectionConfig = {
  host: url.hostname,
  port: url.port || 3306,
  user: url.username,
  password: url.password,
  database: url.pathname.slice(1), // Remove leading slash
};

async function migrateToMySQL() {
  let connection;
  try {
    connection = await mysql.createConnection(connectionConfig);
    console.log('✅ Connected to MySQL database');

    console.log('\n🔄 MIGRATING CONFIGURATIONS FROM MEMORY TO MYSQL...');
    console.log('=' .repeat(60));

    // 1. Migrate Email Templates (based on what I saw in the logs)
    console.log('\n📝 Migrating Email Templates...');
    
    const emailTemplates = [
      {
        templateId: 'tvzyon-template',
        name: 'TVZYON',
        description: 'TVZYON email template for IPTV subscriptions',
        subject: 'Your TVZYON IPTV Subscription Details',
        htmlContent: '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;"><h2 style="color: #333;">Your TVZYON IPTV Subscription</h2><p>Dear {{customerName}},</p><p>Thank you for choosing TVZYON! Your IPTV subscription is ready.</p><div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;"><h3>Subscription Details:</h3><p><strong>Username:</strong> {{username}}<br><strong>Password:</strong> {{password}}</p><p><strong>M3U URL:</strong><br>{{m3uUrl}}</p></div><p>Best regards,<br>TVZYON Team</p></div>',
        textContent: 'Your TVZYON IPTV Subscription\n\nDear {{customerName}},\n\nThank you for choosing TVZYON! Your IPTV subscription is ready.\n\nUsername: {{username}}\nPassword: {{password}}\nM3U URL: {{m3uUrl}}\n\nBest regards,\nTVZYON Team',
        category: 'subscription',
        isDefault: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        templateId: 'directo-template',
        name: 'Directo',
        description: 'Directo email template for IPTV subscriptions',
        subject: 'Your Directo IPTV Subscription Details',
        htmlContent: '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;"><h2 style="color: #333;">Your Directo IPTV Subscription</h2><p>Dear {{customerName}},</p><p>Welcome to Directo IPTV! Your subscription is now active.</p><div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;"><h3>Access Information:</h3><p><strong>Username:</strong> {{username}}<br><strong>Password:</strong> {{password}}</p><p><strong>M3U URL:</strong><br>{{m3uUrl}}</p></div><p>Enjoy your service!<br>Directo Team</p></div>',
        textContent: 'Your Directo IPTV Subscription\n\nDear {{customerName}},\n\nWelcome to Directo IPTV! Your subscription is now active.\n\nUsername: {{username}}\nPassword: {{password}}\nM3U URL: {{m3uUrl}}\n\nEnjoy your service!\nDirecto Team',
        category: 'subscription',
        isDefault: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        templateId: 'avix-template',
        name: 'AVIX',
        description: 'AVIX email template for IPTV subscriptions',
        subject: 'Your AVIX IPTV Subscription Details',
        htmlContent: '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;"><h2 style="color: #333;">Your AVIX IPTV Subscription</h2><p>Dear {{customerName}},</p><p>Thank you for subscribing to AVIX IPTV! Your account is ready to use.</p><div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;"><h3>Login Credentials:</h3><p><strong>Username:</strong> {{username}}<br><strong>Password:</strong> {{password}}</p><p><strong>M3U URL:</strong><br>{{m3uUrl}}</p></div><p>Best regards,<br>AVIX Team</p></div>',
        textContent: 'Your AVIX IPTV Subscription\n\nDear {{customerName}},\n\nThank you for subscribing to AVIX IPTV! Your account is ready to use.\n\nUsername: {{username}}\nPassword: {{password}}\nM3U URL: {{m3uUrl}}\n\nBest regards,\nAVIX Team',
        category: 'subscription',
        isDefault: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        templateId: 'smartonn-template',
        name: 'Smartonn',
        description: 'Smartonn email template for IPTV subscriptions',
        subject: 'Your Smartonn IPTV Subscription Details',
        htmlContent: '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;"><h2 style="color: #333;">Your Smartonn IPTV Subscription</h2><p>Dear {{customerName}},</p><p>Welcome to Smartonn IPTV! Your premium subscription is now active.</p><div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;"><h3>Account Details:</h3><p><strong>Username:</strong> {{username}}<br><strong>Password:</strong> {{password}}</p><p><strong>M3U URL:</strong><br>{{m3uUrl}}</p></div><p>Thank you for choosing Smartonn!<br>Smartonn Support Team</p></div>',
        textContent: 'Your Smartonn IPTV Subscription\n\nDear {{customerName}},\n\nWelcome to Smartonn IPTV! Your premium subscription is now active.\n\nUsername: {{username}}\nPassword: {{password}}\nM3U URL: {{m3uUrl}}\n\nThank you for choosing Smartonn!\nSmartonn Support Team',
        category: 'subscription',
        isDefault: true, // Make Smartonn the default
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
    ];

    for (const template of emailTemplates) {
      try {
        await connection.execute(`
          INSERT INTO email_templates (template_id, name, description, subject, html_content, text_content, category, is_default, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          name = VALUES(name),
          description = VALUES(description),
          subject = VALUES(subject),
          html_content = VALUES(html_content),
          text_content = VALUES(text_content),
          category = VALUES(category),
          is_default = VALUES(is_default),
          updated_at = VALUES(updated_at)
        `, [
          template.templateId, template.name, template.description, template.subject,
          template.htmlContent, template.textContent, template.category, template.isDefault,
          template.createdAt, template.updatedAt
        ]);
        console.log(`✅ Email Template: ${template.name}`);
      } catch (error) {
        console.log(`⚠️ Email Template ${template.name} error:`, error.message);
      }
    }

    // 2. Migrate Payment Gateway URLs (from memory config to database)
    console.log('\n💳 Migrating Payment Gateway URLs...');
    
    // Create custom payment links table if it doesn't exist
    try {
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS custom_payment_links (
          id VARCHAR(255) PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          url TEXT NOT NULL,
          button_text VARCHAR(255) NOT NULL DEFAULT 'Complete Payment',
          success_redirect_url TEXT,
          active BOOLEAN NOT NULL DEFAULT TRUE,
          is_trial BOOLEAN NOT NULL DEFAULT FALSE,
          created_at VARCHAR(255) NOT NULL,
          updated_at VARCHAR(255) NOT NULL
        )
      `);
      console.log('✅ Custom payment links table ready');
    } catch (error) {
      console.log('⚠️ Custom payment links table already exists');
    }

    const paymentLinks = [
      {
        id: 'storazo-link',
        name: 'Storazo',
        url: 'https://storazo.com/checkout/?add-to-cart=893',
        buttonText: 'Complete Payment',
        successRedirectUrl: '',
        active: true,
        isTrial: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'billgang-link',
        name: 'Billgang',
        url: 'https://shippz.bgng.io/product/starter-access',
        buttonText: 'Complete Payment',
        successRedirectUrl: '',
        active: true,
        isTrial: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
    ];

    for (const link of paymentLinks) {
      try {
        await connection.execute(`
          INSERT INTO custom_payment_links (id, name, url, button_text, success_redirect_url, active, is_trial, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          name = VALUES(name),
          url = VALUES(url),
          button_text = VALUES(button_text),
          success_redirect_url = VALUES(success_redirect_url),
          active = VALUES(active),
          is_trial = VALUES(is_trial),
          updated_at = VALUES(updated_at)
        `, [
          link.id, link.name, link.url, link.buttonText, link.successRedirectUrl,
          link.active, link.isTrial, link.createdAt, link.updatedAt
        ]);
        console.log(`✅ Payment Link: ${link.name} (${link.url})`);
      } catch (error) {
        console.log(`⚠️ Payment Link ${link.name} error:`, error.message);
      }
    }

    // 3. Migrate Allowed Emails
    console.log('\n📧 Migrating Allowed Emails...');
    
    const allowedEmails = [
      {
        email: '<EMAIL>',
        notes: 'Default admin email - Hassan Khalid',
        smtpProvider: 'avixiptv-smtp',
        lastUpdated: new Date().toISOString(),
        createdAt: new Date().toISOString(),
      },
      {
        email: '<EMAIL>',
        notes: 'Storazo contact email',
        smtpProvider: 'avixiptv-smtp',
        lastUpdated: new Date().toISOString(),
        createdAt: new Date().toISOString(),
      }
    ];

    for (const email of allowedEmails) {
      try {
        await connection.execute(`
          INSERT INTO allowed_emails (email, notes, smtp_provider, last_updated, created_at)
          VALUES (?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          notes = VALUES(notes),
          smtp_provider = VALUES(smtp_provider),
          last_updated = VALUES(last_updated)
        `, [
          email.email, email.notes, email.smtpProvider, email.lastUpdated, email.createdAt
        ]);
        console.log(`✅ Allowed Email: ${email.email}`);
      } catch (error) {
        console.log(`⚠️ Allowed Email ${email.email} error:`, error.message);
      }
    }

    console.log('\n🎉 MIGRATION COMPLETED SUCCESSFULLY!');
    console.log('\n📋 SUMMARY:');
    console.log('✅ 4 Email Templates migrated (TVZYON, Directo, AVIX, Smartonn)');
    console.log('✅ 2 Payment Gateway URLs migrated');
    console.log('✅ 2 Allowed Emails migrated');
    console.log('✅ Custom Checkout Page "Smartonn TEST" already exists');

    // Verify the migration
    console.log('\n🔍 VERIFICATION:');
    const [emailCount] = await connection.execute(`SELECT COUNT(*) as count FROM email_templates`);
    const [checkoutCount] = await connection.execute(`SELECT COUNT(*) as count FROM custom_checkout_pages`);
    const [smtpCount] = await connection.execute(`SELECT COUNT(*) as count FROM smtp_providers`);
    const [allowedCount] = await connection.execute(`SELECT COUNT(*) as count FROM allowed_emails`);
    
    let paymentCount = 0;
    try {
      const [paymentResult] = await connection.execute(`SELECT COUNT(*) as count FROM custom_payment_links`);
      paymentCount = paymentResult[0].count;
    } catch (error) {
      console.log('⚠️ Custom payment links table verification failed');
    }

    console.log(`📝 Email Templates: ${emailCount[0].count}`);
    console.log(`🛒 Checkout Pages: ${checkoutCount[0].count}`);
    console.log(`💳 Payment Links: ${paymentCount}`);
    console.log(`📧 SMTP Providers: ${smtpCount[0].count}`);
    console.log(`📧 Allowed Emails: ${allowedCount[0].count}`);

    console.log('\n🚀 ALL CONFIGURATIONS NOW STORED IN MYSQL!');
    console.log('🌐 Access your application at: http://localhost:3001');
    console.log('🔧 Admin panel: http://localhost:3001/admin');

  } catch (error) {
    console.error('❌ Error migrating to MySQL:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
    process.exit(0);
  }
}

// Run the migration
migrateToMySQL();
