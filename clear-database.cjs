const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

// Load environment variables
if (process.env.NODE_ENV === 'production') {
  dotenv.config({ path: '.env.production', override: true });
} else {
  dotenv.config();
}

const DATABASE_URL = process.env.DATABASE_URL || 'mysql://user:XwB8rMNKB3OZzBzyYZft@localhost:3306/db';

// Parse MySQL connection string
const url = new URL(DATABASE_URL);
const connectionConfig = {
  host: url.hostname,
  port: url.port || 3306,
  user: url.username,
  password: url.password,
  database: url.pathname.slice(1), // Remove leading slash
};

async function clearDatabase() {
  let connection;
  try {
    connection = await mysql.createConnection(connectionConfig);
    console.log('✅ Connected to MySQL database');

    console.log('\n🧹 CLEARING DATABASE FOR FRESH SETUP...');
    console.log('=' .repeat(50));

    // Clear email templates (keep SMTP providers and allowed emails)
    console.log('📝 Clearing email templates...');
    const [emailResult] = await connection.execute(`
      DELETE FROM email_templates
    `);
    console.log(`✅ Deleted ${emailResult.affectedRows} email templates`);

    // Clear custom checkout pages
    console.log('🛒 Clearing custom checkout pages...');
    const [checkoutResult] = await connection.execute(`
      DELETE FROM custom_checkout_pages
    `);
    console.log(`✅ Deleted ${checkoutResult.affectedRows} custom checkout pages`);

    // Clear custom payment links if table exists
    try {
      console.log('💳 Clearing custom payment links...');
      const [paymentResult] = await connection.execute(`
        DELETE FROM custom_payment_links
      `);
      console.log(`✅ Deleted ${paymentResult.affectedRows} custom payment links`);
    } catch (error) {
      console.log('⚠️ Custom payment links table not found (this is normal)');
    }

    // Clear PayPal buttons
    try {
      console.log('🔘 Clearing PayPal buttons...');
      const [paypalResult] = await connection.execute(`
        DELETE FROM paypal_buttons
      `);
      console.log(`✅ Deleted ${paypalResult.affectedRows} PayPal buttons`);
    } catch (error) {
      console.log('⚠️ PayPal buttons table not found (this is normal)');
    }

    // Clear system messages that might contain old configurations
    try {
      console.log('📋 Clearing system messages...');
      const [systemResult] = await connection.execute(`
        DELETE FROM system_messages WHERE category IN ('payment', 'checkout', 'template')
      `);
      console.log(`✅ Deleted ${systemResult.affectedRows} system messages`);
    } catch (error) {
      console.log('⚠️ System messages table not found (this is normal)');
    }

    // Reset auto-increment counters
    console.log('\n🔄 Resetting auto-increment counters...');
    try {
      await connection.execute(`ALTER TABLE email_templates AUTO_INCREMENT = 1`);
      await connection.execute(`ALTER TABLE custom_checkout_pages AUTO_INCREMENT = 1`);
      console.log('✅ Auto-increment counters reset');
    } catch (error) {
      console.log('⚠️ Could not reset auto-increment counters (this is normal)');
    }

    console.log('\n🎉 DATABASE CLEARED SUCCESSFULLY!');
    console.log('\n📋 WHAT WAS KEPT:');
    console.log('✅ SMTP Providers (AVIXIPTV, DirectoIPTV, Enzidswan, Smartonn, TVZYON)');
    console.log('✅ Allowed Emails (<EMAIL>)');
    console.log('✅ User accounts and authentication');
    console.log('✅ Database structure and tables');

    console.log('\n📋 WHAT WAS CLEARED:');
    console.log('❌ Email Templates (ready for your TVZYON, Directo, AVIX, Smartonn templates)');
    console.log('❌ Custom Checkout Pages (ready for your "Smartonn TEST" page)');
    console.log('❌ Payment Gateway configurations (ready for your URLs)');
    console.log('❌ PayPal buttons and custom payment links');

    console.log('\n🚀 NEXT STEPS:');
    console.log('1. Go to http://localhost:3001/admin');
    console.log('2. Create your email templates:');
    console.log('   - TVZYON');
    console.log('   - Directo');
    console.log('   - AVIX');
    console.log('   - Smartonn');
    console.log('3. Create your custom checkout page: "Smartonn TEST"');
    console.log('4. Set up your payment gateway URLs:');
    console.log('   - https://shippz.bgng.io/product/starter-access');
    console.log('   - https://storazo.com/checkout/?add-to-cart=893');
    console.log('5. Tell me when you\'re done so I can save everything as defaults!');

    // Verify the cleanup
    console.log('\n🔍 VERIFICATION:');
    const [emailCount] = await connection.execute(`SELECT COUNT(*) as count FROM email_templates`);
    const [checkoutCount] = await connection.execute(`SELECT COUNT(*) as count FROM custom_checkout_pages`);
    const [smtpCount] = await connection.execute(`SELECT COUNT(*) as count FROM smtp_providers`);
    const [allowedCount] = await connection.execute(`SELECT COUNT(*) as count FROM allowed_emails`);

    console.log(`📝 Email Templates: ${emailCount[0].count} (should be 0)`);
    console.log(`🛒 Checkout Pages: ${checkoutCount[0].count} (should be 0)`);
    console.log(`📧 SMTP Providers: ${smtpCount[0].count} (should be 5)`);
    console.log(`📧 Allowed Emails: ${allowedCount[0].count} (should be 1)`);

  } catch (error) {
    console.error('❌ Error clearing database:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
    process.exit(0);
  }
}

// Run the cleanup
clearDatabase();
