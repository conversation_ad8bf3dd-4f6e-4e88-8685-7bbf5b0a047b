#!/usr/bin/env node

/**
 * HASSAN'S DIGITAL INVOICE SYSTEM - DEFAULT CONFIGURATION
 * Generated on: 2025-06-02T19:07:58.104Z
 * 
 * This script contains all your custom configurations:
 * - Email Templates: TVZYON, Directo, AVIX, Smartonn
 * - Custom Checkout Page: Smartonn TEST
 * - Payment Gateway URLs: Storazo, Billgang
 * - SMTP Providers: 5 configured providers
 * - Allowed Emails: Your approved email addresses
 */

const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

// Load environment variables
if (process.env.NODE_ENV === 'production') {
  dotenv.config({ path: '.env.production', override: true });
} else {
  dotenv.config();
}

const DATABASE_URL = process.env.DATABASE_URL || 'mysql://user:XwB8rMNKB3OZzBzyYZft@localhost:3306/db';

// Parse MySQL connection string
const url = new URL(DATABASE_URL);
const connectionConfig = {
  host: url.hostname,
  port: url.port || 3306,
  user: url.username,
  password: url.password,
  database: url.pathname.slice(1),
};

// Your Default Email Templates
const defaultEmailTemplates = [
  {
    "template_id": "avix-template",
    "name": "AVIX",
    "description": "AVIX email template for IPTV subscriptions",
    "subject": "Your AVIX IPTV Subscription Details",
    "html_content": "<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\"><h2 style=\"color: #333;\">Your AVIX IPTV Subscription</h2><p>Dear {{customerName}},</p><p>Thank you for subscribing to AVIX IPTV! Your account is ready to use.</p><div style=\"background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;\"><h3>Login Credentials:</h3><p><strong>Username:</strong> {{username}}<br><strong>Password:</strong> {{password}}</p><p><strong>M3U URL:</strong><br>{{m3uUrl}}</p></div><p>Best regards,<br>AVIX Team</p></div>",
    "text_content": "Your AVIX IPTV Subscription\n\nDear {{customerName}},\n\nThank you for subscribing to AVIX IPTV! Your account is ready to use.\n\nUsername: {{username}}\nPassword: {{password}}\nM3U URL: {{m3uUrl}}\n\nBest regards,\nAVIX Team",
    "category": "subscription",
    "is_default": 0,
    "created_at": "2025-06-02T19:04:29.039Z",
    "updated_at": "2025-06-02T19:04:29.039Z"
  },
  {
    "template_id": "directo-template",
    "name": "Directo",
    "description": "Directo email template for IPTV subscriptions",
    "subject": "Your Directo IPTV Subscription Details",
    "html_content": "<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\"><h2 style=\"color: #333;\">Your Directo IPTV Subscription</h2><p>Dear {{customerName}},</p><p>Welcome to Directo IPTV! Your subscription is now active.</p><div style=\"background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;\"><h3>Access Information:</h3><p><strong>Username:</strong> {{username}}<br><strong>Password:</strong> {{password}}</p><p><strong>M3U URL:</strong><br>{{m3uUrl}}</p></div><p>Enjoy your service!<br>Directo Team</p></div>",
    "text_content": "Your Directo IPTV Subscription\n\nDear {{customerName}},\n\nWelcome to Directo IPTV! Your subscription is now active.\n\nUsername: {{username}}\nPassword: {{password}}\nM3U URL: {{m3uUrl}}\n\nEnjoy your service!\nDirecto Team",
    "category": "subscription",
    "is_default": 0,
    "created_at": "2025-06-02T19:04:29.039Z",
    "updated_at": "2025-06-02T19:04:29.039Z"
  },
  {
    "template_id": "smartonn-template",
    "name": "Smartonn",
    "description": "Smartonn email template for IPTV subscriptions",
    "subject": "Your Smartonn IPTV Subscription Details",
    "html_content": "<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\"><h2 style=\"color: #333;\">Your Smartonn IPTV Subscription</h2><p>Dear {{customerName}},</p><p>Welcome to Smartonn IPTV! Your premium subscription is now active.</p><div style=\"background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;\"><h3>Account Details:</h3><p><strong>Username:</strong> {{username}}<br><strong>Password:</strong> {{password}}</p><p><strong>M3U URL:</strong><br>{{m3uUrl}}</p></div><p>Thank you for choosing Smartonn!<br>Smartonn Support Team</p></div>",
    "text_content": "Your Smartonn IPTV Subscription\n\nDear {{customerName}},\n\nWelcome to Smartonn IPTV! Your premium subscription is now active.\n\nUsername: {{username}}\nPassword: {{password}}\nM3U URL: {{m3uUrl}}\n\nThank you for choosing Smartonn!\nSmartonn Support Team",
    "category": "subscription",
    "is_default": 1,
    "created_at": "2025-06-02T19:04:29.039Z",
    "updated_at": "2025-06-02T19:04:29.039Z"
  },
  {
    "template_id": "tvzyon-template",
    "name": "TVZYON",
    "description": "TVZYON email template for IPTV subscriptions",
    "subject": "Your TVZYON IPTV Subscription Details",
    "html_content": "<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\"><h2 style=\"color: #333;\">Your TVZYON IPTV Subscription</h2><p>Dear {{customerName}},</p><p>Thank you for choosing TVZYON! Your IPTV subscription is ready.</p><div style=\"background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;\"><h3>Subscription Details:</h3><p><strong>Username:</strong> {{username}}<br><strong>Password:</strong> {{password}}</p><p><strong>M3U URL:</strong><br>{{m3uUrl}}</p></div><p>Best regards,<br>TVZYON Team</p></div>",
    "text_content": "Your TVZYON IPTV Subscription\n\nDear {{customerName}},\n\nThank you for choosing TVZYON! Your IPTV subscription is ready.\n\nUsername: {{username}}\nPassword: {{password}}\nM3U URL: {{m3uUrl}}\n\nBest regards,\nTVZYON Team",
    "category": "subscription",
    "is_default": 0,
    "created_at": "2025-06-02T19:04:29.039Z",
    "updated_at": "2025-06-02T19:04:29.039Z"
  }
];

// Your Default Custom Checkout Pages
const defaultCheckoutPages = [
  {
    "id": 1,
    "title": "Smartonn TEST",
    "slug": "smartonn-test",
    "product_name": "IPTV 24 Hours Trial",
    "product_description": "Experience premium IPTV service with our 24-hour trial. Access thousands of channels worldwide including sports, movies, TV shows, and more. Perfect for testing our service quality before committing to a full subscription.",
    "price": "10.00",
    "image_url": "/uploads/IPTV-24h.jpg",
    "payment_method": "custom-link",
    "custom_payment_link_id": "storazo-link",
    "paypal_button_id": "",
    "embed_code_id": "",
    "require_allowed_email": 1,
    "is_trial_checkout": 1,
    "confirmation_message": "<div class=\"space-y-3\"><p><strong>🎯 Ready to start your trial?</strong></p><p>✅ <strong>What you're getting:</strong></p><ul class=\"list-disc list-inside space-y-1\"><li>24 hours of premium IPTV access</li><li>Thousands of channels worldwide</li><li>HD/4K quality streaming</li><li>All device compatibility</li><li>Instant activation</li><li>Full customer support</li></ul><p class=\"text-sm text-muted-foreground mt-3\">💳 <em>Secure payment processing via Storazo</em></p><p class=\"text-xs text-muted-foreground\">After payment, you will receive your trial credentials within minutes.</p></div>",
    "header_title": "Smartonn IPTV Trial",
    "footer_text": "Secure payment • Instant delivery • 24/7 support",
    "header_logo": "",
    "footer_logo": "",
    "theme_mode": "light",
    "use_referrer_masking": 0,
    "redirect_delay": 2000,
    "expires_at": null,
    "active": 1,
    "views": 0,
    "conversions": 0,
    "created_at": "2025-06-02T19:05:26.162Z",
    "updated_at": "2025-06-02T19:05:26.163Z"
  }
];

// Your Default Payment Gateway URLs
const defaultPaymentLinks = [
  {
    "id": "billgang-link",
    "name": "Billgang",
    "url": "https://shippz.bgng.io/product/starter-access",
    "button_text": "Complete Payment",
    "success_redirect_url": "",
    "active": 1,
    "is_trial": 1,
    "created_at": "2025-06-02T19:04:29.093Z",
    "updated_at": "2025-06-02T19:04:29.093Z"
  },
  {
    "id": "storazo-link",
    "name": "Storazo",
    "url": "https://storazo.com/checkout/?add-to-cart=893",
    "button_text": "Complete Payment",
    "success_redirect_url": "",
    "active": 1,
    "is_trial": 0,
    "created_at": "2025-06-02T19:04:29.093Z",
    "updated_at": "2025-06-02T19:04:29.093Z"
  }
];

// Your Default SMTP Providers
const defaultSmtpProviders = [
  {
    "id": "avixiptv-smtp",
    "name": "AVIXIPTV",
    "host": "mail.avixiptv.com",
    "port": "587",
    "secure": 0,
    "username": "<EMAIL>",
    "password": "Avix@2024",
    "from_email": "<EMAIL>",
    "from_name": "AVIXIPTV Support",
    "active": 1,
    "is_default": 1,
    "is_backup": 0,
    "created_at": "2025-06-01T18:30:35.034Z",
    "updated_at": "2025-06-02T18:36:44.668Z"
  },
  {
    "id": "directoiptv-smtp",
    "name": "DirectoIPTV",
    "host": "mail.directoiptv.com",
    "port": "587",
    "secure": 0,
    "username": "<EMAIL>",
    "password": "Directo@2024",
    "from_email": "<EMAIL>",
    "from_name": "DirectoIPTV Support",
    "active": 1,
    "is_default": 0,
    "is_backup": 1,
    "created_at": "2025-06-01T18:30:35.034Z",
    "updated_at": "2025-06-02T18:36:44.668Z"
  },
  {
    "id": "enzidswan-smtp",
    "name": "Enzidswan",
    "host": "mail.enzidswan.com",
    "port": "587",
    "secure": 0,
    "username": "<EMAIL>",
    "password": "Enzid@2024",
    "from_email": "<EMAIL>",
    "from_name": "Enzidswan Support",
    "active": 1,
    "is_default": 0,
    "is_backup": 0,
    "created_at": "2025-06-01T18:30:35.034Z",
    "updated_at": "2025-06-02T18:36:44.668Z"
  },
  {
    "id": "smartonn-smtp",
    "name": "Smartonn",
    "host": "mail.smartonn.com",
    "port": "587",
    "secure": 0,
    "username": "<EMAIL>",
    "password": "Smart@2024",
    "from_email": "<EMAIL>",
    "from_name": "Smartonn Support",
    "active": 1,
    "is_default": 0,
    "is_backup": 0,
    "created_at": "2025-06-01T18:30:35.034Z",
    "updated_at": "2025-06-02T18:36:44.668Z"
  },
  {
    "id": "tvzyon-smtp",
    "name": "TVZYON",
    "host": "mail.tvzyon.com",
    "port": "587",
    "secure": 0,
    "username": "<EMAIL>",
    "password": "Tvzyon@2024",
    "from_email": "<EMAIL>",
    "from_name": "TVZYON Support",
    "active": 1,
    "is_default": 0,
    "is_backup": 0,
    "created_at": "2025-06-01T18:30:35.034Z",
    "updated_at": "2025-06-02T18:36:44.668Z"
  }
];

// Your Default Allowed Emails
const defaultAllowedEmails = [
  {
    "id": 1,
    "email": "<EMAIL>",
    "notes": "Default admin email - Hassan Khalid",
    "smtp_provider": "avixiptv-smtp",
    "last_updated": "2025-06-02T19:04:29.105Z",
    "created_at": "2025-06-02T18:36:44.668Z"
  },
  {
    "id": 3,
    "email": "<EMAIL>",
    "notes": "Storazo contact email",
    "smtp_provider": "avixiptv-smtp",
    "last_updated": "2025-06-02T19:04:29.105Z",
    "created_at": "2025-06-02T19:04:29.105Z"
  }
];

async function setupDefaults() {
  let connection;
  try {
    connection = await mysql.createConnection(connectionConfig);
    console.log('✅ Connected to MySQL database');
    console.log('🚀 Setting up Hassan\'s default configurations...');

    // Setup Email Templates
    console.log('\n📝 Setting up email templates...');
    for (const template of defaultEmailTemplates) {
      try {
        await connection.execute(`
          INSERT INTO email_templates (template_id, name, description, subject, html_content, text_content, category, is_default, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          name = VALUES(name),
          description = VALUES(description),
          subject = VALUES(subject),
          html_content = VALUES(html_content),
          text_content = VALUES(text_content),
          category = VALUES(category),
          is_default = VALUES(is_default),
          updated_at = VALUES(updated_at)
        `, [
          template.template_id, template.name, template.description, template.subject,
          template.html_content, template.text_content, template.category, template.is_default,
          template.created_at, template.updated_at
        ]);
        console.log(`✅ Email Template: ${template.name}`);
      } catch (error) {
        console.log(`⚠️ Email Template ${template.name} error:`, error.message);
      }
    }

    // Setup Custom Checkout Pages
    console.log('\n🛒 Setting up custom checkout pages...');
    for (const page of defaultCheckoutPages) {
      try {
        await connection.execute(`
          INSERT INTO custom_checkout_pages (
            title, slug, product_name, product_description, price, image_url, payment_method,
            custom_payment_link_id, paypal_button_id, embed_code_id, require_allowed_email, 
            is_trial_checkout, confirmation_message, header_title, footer_text, header_logo, 
            footer_logo, theme_mode, use_referrer_masking, redirect_delay, expires_at,
            active, views, conversions, created_at, updated_at
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          title = VALUES(title),
          product_name = VALUES(product_name),
          product_description = VALUES(product_description),
          price = VALUES(price),
          updated_at = VALUES(updated_at)
        `, [
          page.title, page.slug, page.product_name, page.product_description, page.price, 
          page.image_url, page.payment_method, page.custom_payment_link_id, page.paypal_button_id,
          page.embed_code_id, page.require_allowed_email, page.is_trial_checkout,
          page.confirmation_message, page.header_title, page.footer_text, page.header_logo, 
          page.footer_logo, page.theme_mode, page.use_referrer_masking, page.redirect_delay, 
          page.expires_at, page.active, page.views, page.conversions, page.created_at, page.updated_at
        ]);
        console.log(`✅ Checkout Page: ${page.title} (${page.slug})`);
      } catch (error) {
        console.log(`⚠️ Checkout Page ${page.title} error:`, error.message);
      }
    }

    // Setup Payment Links
    console.log('\n💳 Setting up payment gateway URLs...');
    
    // Create table if it doesn't exist
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS custom_payment_links (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        url TEXT NOT NULL,
        button_text VARCHAR(255) NOT NULL DEFAULT 'Complete Payment',
        success_redirect_url TEXT,
        active BOOLEAN NOT NULL DEFAULT TRUE,
        is_trial BOOLEAN NOT NULL DEFAULT FALSE,
        created_at VARCHAR(255) NOT NULL,
        updated_at VARCHAR(255) NOT NULL
      )
    `);

    for (const link of defaultPaymentLinks) {
      try {
        await connection.execute(`
          INSERT INTO custom_payment_links (id, name, url, button_text, success_redirect_url, active, is_trial, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          name = VALUES(name),
          url = VALUES(url),
          updated_at = VALUES(updated_at)
        `, [
          link.id, link.name, link.url, link.button_text, link.success_redirect_url,
          link.active, link.is_trial, link.created_at, link.updated_at
        ]);
        console.log(`✅ Payment Link: ${link.name} (${link.url})`);
      } catch (error) {
        console.log(`⚠️ Payment Link ${link.name} error:`, error.message);
      }
    }

    // Setup SMTP Providers
    console.log('\n📧 Setting up SMTP providers...');
    for (const provider of defaultSmtpProviders) {
      try {
        await connection.execute(`
          INSERT INTO smtp_providers (id, name, host, port, secure, username, password, from_email, from_name, active, is_default, is_backup, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          name = VALUES(name),
          host = VALUES(host),
          active = VALUES(active),
          is_default = VALUES(is_default),
          is_backup = VALUES(is_backup),
          updated_at = VALUES(updated_at)
        `, [
          provider.id, provider.name, provider.host, provider.port, provider.secure,
          provider.username, provider.password, provider.from_email, provider.from_name,
          provider.active, provider.is_default, provider.is_backup, provider.created_at, provider.updated_at
        ]);
        console.log(`✅ SMTP Provider: ${provider.name}`);
      } catch (error) {
        console.log(`⚠️ SMTP Provider ${provider.name} error:`, error.message);
      }
    }

    // Setup Allowed Emails
    console.log('\n📧 Setting up allowed emails...');
    for (const email of defaultAllowedEmails) {
      try {
        await connection.execute(`
          INSERT INTO allowed_emails (email, notes, smtp_provider, last_updated, created_at)
          VALUES (?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          notes = VALUES(notes),
          smtp_provider = VALUES(smtp_provider),
          last_updated = VALUES(last_updated)
        `, [
          email.email, email.notes, email.smtp_provider, email.last_updated, email.created_at
        ]);
        console.log(`✅ Allowed Email: ${email.email}`);
      } catch (error) {
        console.log(`⚠️ Allowed Email ${email.email} error:`, error.message);
      }
    }

    console.log('\n🎉 HASSAN\'S DEFAULT CONFIGURATION SETUP COMPLETED!');
    console.log('\n📋 SUMMARY:');
    console.log(`✅ ${defaultEmailTemplates.length} Email Templates (TVZYON, Directo, AVIX, Smartonn)`);
    console.log(`✅ ${defaultCheckoutPages.length} Custom Checkout Page (Smartonn TEST)`);
    console.log(`✅ ${defaultPaymentLinks.length} Payment Gateway URLs (Storazo, Billgang)`);
    console.log(`✅ ${defaultSmtpProviders.length} SMTP Providers`);
    console.log(`✅ ${defaultAllowedEmails.length} Allowed Emails`);
    console.log('\n🌐 Access your application at: http://localhost:3001');
    console.log('🔧 Admin panel: http://localhost:3001/admin');
    console.log('🛒 Smartonn TEST checkout: http://localhost:3001/checkout/smartonn-test');

  } catch (error) {
    console.error('❌ Error setting up defaults:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
    process.exit(0);
  }
}

// Run the setup
if (require.main === module) {
  setupDefaults();
}

module.exports = { setupDefaults, defaultEmailTemplates, defaultCheckoutPages, defaultPaymentLinks, defaultSmtpProviders, defaultAllowedEmails };
